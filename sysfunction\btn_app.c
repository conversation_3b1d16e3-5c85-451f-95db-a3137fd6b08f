
#include "bsp_gd32f470vet6.h"

extern uint8_t ucLed[6];

uint8_t KeyNum = Init_Key;
uint8_t pre_KeyNum ;
typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_6,
    USER_BUTTON_MAX,

    //    USER_BUTTON_COMBO_0 = 0x100,
    //    USER_BUTTON_COMBO_1,
    //    USER_BUTTON_COMBO_2,
    //    USER_BUTTON_COMBO_3,
    //    USER_BUTTON_COMBO_MAX,
} user_button_t;

/*  Debounce time in milliseconds, Debounce time in milliseconds for release event, Minimum pressed time for valid click event, Maximum ...,
    Maximum time between 2 clicks to be considered consecutive click, Time in ms for periodic keep alive event, Max number of consecutive clicks */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_6, &defaul_ebtn_param),
};

// static ebtn_btn_combo_t btns_combo[] = {
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//     EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
// };

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch (btn->key_id)
    {
    case USER_BUTTON_0:
        return !KEY1_READ;
    case USER_BUTTON_1:
        return !KEY2_READ;
    case USER_BUTTON_2:
        return !KEY3_READ;
    case USER_BUTTON_3:
        return !KEY4_READ;
    case USER_BUTTON_4:
        return !KEY5_READ;
    case USER_BUTTON_5:
        return !KEY6_READ;
    case USER_BUTTON_6:
        return !KEYW_READ;
    default:
        return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if (evt == EBTN_EVT_ONCLICK)
    {
        switch (btn->key_id)
        {
        case USER_BUTTON_0:
            LED1_TOGGLE;
            break;
        case USER_BUTTON_1:
            LED2_TOGGLE;
            break;
        case USER_BUTTON_2:
            LED3_TOGGLE;
            break;
        case USER_BUTTON_3:
            LED4_TOGGLE;
            break;
        case USER_BUTTON_4:
            LED5_TOGGLE;
            break;
        case USER_BUTTON_5:
            LED6_TOGGLE;
            break;
        case USER_BUTTON_6:
            LED6_TOGGLE;
            break;
        default:
            break;
        }
    }
}

void app_btn_init(void)
{
    // ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);

    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
    //    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
    //    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);
}

// 按键状态结构体
typedef struct {
    uint8_t current_state;
    uint8_t last_state;
    uint32_t press_time;
    uint8_t debounce_count;
    uint8_t is_pressed;
} key_state_t;

// 按键状态数组
static key_state_t key_states[4] = {0}; // KEY3, KEY4, KEY5, KEY6

// 非阻塞按键处理函数
uint8_t process_key_non_blocking(uint8_t key_id, uint8_t key_read)
{
    key_state_t *key = &key_states[key_id];
    uint32_t current_time = get_system_ms();

    key->current_state = !key_read; // 按键按下为1

    // 状态变化检测
    if (key->current_state != key->last_state) {
        key->press_time = current_time;
        key->debounce_count = 0;
        key->last_state = key->current_state;
    }

    // 防抖处理
    if (current_time - key->press_time > 20) { // 20ms防抖
        if (key->current_state && !key->is_pressed) {
            key->is_pressed = 1;
            return 1; // 按键按下事件
        } else if (!key->current_state && key->is_pressed) {
            key->is_pressed = 0;
        }
    }

    return 0; // 无按键事件
}

void btn_task(void)
{
    // 非阻塞按键处理
    if (process_key_non_blocking(0, KEY3_READ)) {
        pre_KeyNum = 1;
        store_log_entry("sample stop (key press)");
        reset_data_storage_system();
    }

    if (process_key_non_blocking(1, KEY4_READ)) {
        KeyNum = 2;
        my_printf(DEBUG_USART, "sample cycle adjust: 5s\r\n");
        store_log_entry("cycle switch to 5s (key press)");
        flash_keynum_stored_silent(KeyNum);
    }

    if (process_key_non_blocking(2, KEY6_READ)) {
        KeyNum = 3;
        my_printf(DEBUG_USART, "sample cycle adjust: 10s\r\n");
        store_log_entry("cycle switch to 10s (key press)");
        flash_keynum_stored_silent(KeyNum);
    }

    if (process_key_non_blocking(3, KEY5_READ)) {
        KeyNum = 4;
        my_printf(DEBUG_USART, "sample cycle adjust: 15s\r\n");
        store_log_entry("cycle switch to 15s (key press)");
        flash_keynum_stored_silent(KeyNum);
    }
}
