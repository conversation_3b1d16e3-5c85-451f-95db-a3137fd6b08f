#ifndef __RTC_APP_H__
#define __RTC_APP_H__
#include "bsp_gd32f470vet6.h"


#ifdef __cplusplus
extern "C" {
#endif

// RTC状态定义
typedef enum {
    RTC_STATUS_OK = 0,
    RTC_STATUS_ERROR = 1
} RTC_StatusTypeDef;

// 函数声明
void RTC_Init(void);
void rtc_task(void);
void rtc_setup(void);
void rtc_show_time(void);
void rtc_show_set_time(void);
void rtc_pre_config(void);
uint8_t rtc_set_time(uint8_t year, uint8_t month, uint8_t date, uint8_t day_of_week,
                     uint8_t hour, uint8_t minute, uint8_t second);
RTC_StatusTypeDef rtc_set_time_from_string_ex(const char *time_str);
RTC_StatusTypeDef rtc_set_time_from_string_debug(const char *time_str);
void rtc_setup_with_params(void);
void rtc_setup_direct(void);
void rtc_adcshow_time(void);
void get_current_time_gd32(rtc_parameter_struct *time);
void get_current_time(rtc_parameter_struct *time);  // 获取当前时间

uint32_t rtc_to_unix_timestamp_hex(void);

// 输入处理函数
uint8_t rtc_is_waiting_datetime_input(void);
void rtc_handle_datetime_command(void);


extern uint8_t oled_flag;

#ifdef __cplusplus
}
#endif

#endif /* __RTC_APP_H__ */
