# DSB系统完整流程图集合

本文件包含了DSB数据采集与存储系统的所有流程图，专门设计为简洁清晰的独立图表，方便截取和使用。

## � 图表索引

### 系统架构图表
- [1. 系统总体架构](#1-系统总体架构)
- [2. 硬件模块架构](#2-硬件模块架构)
- [3. 软件模块架构](#3-软件模块架构)

### 初始化流程图表
- [4. 系统启动流程](#4-系统启动流程)
- [5. 硬件初始化流程](#5-硬件初始化流程)
- [6. 参数配置流程](#6-参数配置流程)

### 任务调度图表
- [7. 任务调度器架构](#7-任务调度器架构)
- [8. 任务执行时序](#8-任务执行时序)
- [9. 任务优先级管理](#9-任务优先级管理)

### 数据处理图表
- [10. ADC采集流程](#10-adc采集流程)
- [11. 数据处理流程](#11-数据处理流程)
- [12. 超限检测流程](#12-超限检测流程)

### 存储系统图表
- [13. 存储系统架构](#13-存储系统架构)
- [14. 文件管理流程](#14-文件管理流程)
- [15. 数据分类存储](#15-数据分类存储)

### 通信系统图表
- [16. 串口通信架构](#16-串口通信架构)
- [17. 命令解析流程](#17-命令解析流程)
- [18. 环形缓冲区机制](#18-环形缓冲区机制)

### 用户交互图表
- [19. 按键处理流程](#19-按键处理流程)
- [20. 显示系统架构](#20-显示系统架构)
- [21. 状态指示系统](#21-状态指示系统)

### 时间管理图表
- [22. RTC时间管理](#22-rtc时间管理)
- [23. 定时器中断处理](#23-定时器中断处理)
- [24. 采样周期控制](#24-采样周期控制)

### 系统状态图表
- [25. 系统状态机](#25-系统状态机)
- [26. 错误处理机制](#26-错误处理机制)
- [27. 系统监控机制](#27-系统监控机制)

## 🎨 图表设计说明

### 颜色编码
- **蓝色** (#e1f5fe)：系统/模块入口
- **绿色** (#c8e6c9)：正常流程/成功状态
- **黄色** (#fff9c4)：判断/决策节点
- **橙色** (#fff3e0)：处理/执行节点
- **粉色** (#fce4ec)：数据/信息节点
- **红色** (#ffebee)：错误/异常处理
- **紫色** (#f3e5f5)：配置/参数节点

### 图表特点
- **简洁明了**：每个图表专注单一功能或模块
- **字体清晰**：适中的节点大小，便于截图使用
- **逻辑清晰**：流程方向明确，易于理解
- **独立完整**：每个图表都是完整的功能单元

## � 使用建议

### 截图使用
1. 在Mermaid Live Editor中打开单个图表
2. 调整合适的缩放比例
3. 截取清晰的图片
4. 可直接插入到文档中

### 编辑修改
1. 复制对应的Mermaid代码
2. 根据需要修改节点内容或样式
3. 保持图表的简洁性和可读性

---
