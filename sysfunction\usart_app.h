#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "stdint.h"
#include "gd32f4xx.h"

#ifdef __cplusplus
extern "C" {
#endif
extern uint8_t radio_Num;
extern uint8_t limit_Num;
extern uint8_t temp_buffer[128];
int my_printf(uint32_t usart_periph, const char *format, ...);
void uart_task(void);

void parse_uart_command(uint8_t *buffer, uint16_t length);

void flash_ratio_threshold_readwrite(void);

FlagStatus rtc_set_time_from_string(const char *time_str);

#ifdef __cplusplus
}
#endif

#endif

