/*------------------------------------------------------------------------*/
/* Unicode handling functions for FatFs                                  */
/*------------------------------------------------------------------------*/

#include "ff.h"

#if _USE_LFN != 0

/*------------------------------------------------------------------------*/
/* Unicode to OEM code conversion                                        */
/*------------------------------------------------------------------------*/

WCHAR ff_convert (	/* Converted code, 0 means conversion error */
	WCHAR	chr,	/* Character code to be converted */
	UINT	dir		/* 0: Unicode to OEM code, 1: OEM code to Unicode */
)
{
	if (chr < 0x80) {	/* ASCII */
		return chr;
	}
	if (dir) {	/* OEM code to Unicode */
		/* Add OEM code to Unicode conversion here if needed */
		return chr;
	} else {	/* Unicode to OEM code */
		/* Add Unicode to OEM code conversion here if needed */
		return chr;
	}
}

/*------------------------------------------------------------------------*/
/* Unicode upper case conversion                                          */
/*------------------------------------------------------------------------*/

WCHAR ff_wtoupper (	/* Upper converted character */
	WCHAR chr		/* Input character */
)
{
	/* Basic ASCII upper case conversion */
	if (chr >= 'a' && chr <= 'z') {
		return chr - 0x20;
	}
	
	/* Add more Unicode upper case conversion here if needed */
	return chr;
}

#endif /* _USE_LFN != 0 */
