
#include "bsp_gd32f470vet6.h"

// 系统健康检查计数器
static uint32_t system_health_counter = 0;
static uint8_t system_error_count = 0;

// 看门狗初始化
void watchdog_init(void)
{
    // 配置独立看门狗：4秒超时
    fwdgt_config(FWDGT_PRESCALER_DIV256, 0x0FFF);
    fwdgt_enable();
}

// 喂狗函数
void watchdog_feed(void)
{
    fwdgt_counter_reload();
}

// 系统健康检查
uint8_t system_health_check(void)
{
    system_health_counter++;

    // 每1000次循环检查一次系统状态
    if (system_health_counter % 1000 == 0) {
        // 检查关键系统状态
        if (sd_init_success == 0) {
            system_error_count++;
        }

        // 如果错误次数过多，返回错误
        if (system_error_count > 10) {
            return 0; // 系统异常
        }
    }

    return 1; // 系统正常
}

int main(void)
{
    // 初始化看门狗
    watchdog_init();

    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();

    bsp_adc_init();
    sd_fatfs_init();
    OLED_Init();

	flash_stored();
	flash_ratio_threshold_init();
	power_on_count_init();  // 初始化开机计数器
    scheduler_init();
		all_timer_init();

		my_printf(DEBUG_USART, "====system init====\n");

		// 记录系统初始化
		store_log_entry("system init");

		//打印队伍编号
		my_printf(DEBUG_USART, "Device_ID:2025-CIMC-");
		flash_read_direct(Device_ID_ADDR,20);

		// 记录硬件测试
		store_log_entry("system hardware test");

		// 检查SD卡状态
		if (sd_init_success) {
			store_log_entry("test ok");
		} else {
			store_log_entry("test error: tf card not found");
		}

		my_printf(DEBUG_USART, "\n====system ready====\n");
		

		
    while(1) {
        // 使用改进的调度器，包含安全保护机制
        scheduler_run();

        // 系统健康检查和看门狗喂狗
        if (system_health_check()) {
            watchdog_feed(); // 系统正常时喂狗
        }

        // 可选：使用高级调度器（需要包含scheduler_advanced.h）
        // scheduler_run_hybrid();  // 混合调度器

        // 定期检查调度器状态（每10秒一次）
        static uint32_t last_stats_time = 0;
        uint32_t current_time = get_system_ms();
        if (current_time - last_stats_time >= 10000) {
            // scheduler_get_stats();  // 打印调度统计信息
            last_stats_time = current_time;
        }
    }
}


int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}

