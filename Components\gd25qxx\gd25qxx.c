#include "bsp_gd32f470vet6.h"

#define WRITE 0x02 /* write to memory instruction */
#define WRSR 0x01  /* write status register instruction */
#define WREN 0x06  /* write enable instruction */

#define READ 0x03 /* read from memory instruction */
#define RDSR 0x05 /* read status register instruction  */
#define RDID 0x9F /* read identification */
#define SE 0x20   /* sector erase instruction */
#define BE 0xC7   /* bulk erase instruction */

#define WIP_FLAG 0x01 /* write in progress(wip)flag */
#define DUMMY_BYTE 0xA5

extern uint8_t spi1_send_array[ARRAYSIZE];    // SPI1 DMA 发送缓冲区
extern uint8_t spi1_receive_array[ARRAYSIZE]; // SPI1 DMA 接收缓冲区

/**
 * @brief Initializes the SPI Flash chip.
 * @note This function assumes that the SPI2 peripheral and CS GPIO (PB12)
 *       have already been initialized elsewhere in the application code.
 *       It primarily ensures the CS pin is high (chip deselected) initially.
 *       You can add a Flash ID read here for an initial check if desired.
 */
void spi_flash_init(void)
{
    /* SPI Flash 片选信号默认为高电平（取消选中状态） */
    SPI_FLASH_CS_HIGH();
    
    /* 使能 SPI1 */
    spi_enable(SPI_FLASH);
    
    /* 可选：读取 Flash ID 来验证通信是否正常 */
    // uint32_t id = spi_flash_read_id();
    // 可以添加代码检查 ID 或打印用于调试
}

void spi_flash_sector_erase(uint32_t sector_addr)
{
    spi_flash_write_enable();

    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(SE);
    spi_flash_send_byte_dma((sector_addr & 0xFF0000) >> 16);
    spi_flash_send_byte_dma((sector_addr & 0xFF00) >> 8);
    spi_flash_send_byte_dma(sector_addr & 0xFF);
    SPI_FLASH_CS_HIGH();

    spi_flash_wait_for_write_end();
}

void spi_flash_bulk_erase(void)
{
    spi_flash_write_enable();

    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(BE);
    SPI_FLASH_CS_HIGH();

    spi_flash_wait_for_write_end();
}

void spi_flash_page_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    spi_flash_write_enable();

    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(WRITE);
    spi_flash_send_byte_dma((write_addr & 0xFF0000) >> 16);
    spi_flash_send_byte_dma((write_addr & 0xFF00) >> 8);
    spi_flash_send_byte_dma(write_addr & 0xFF);

    while (num_byte_to_write--)
    {
        spi_flash_send_byte_dma(*pbuffer);
        pbuffer++;
    }

    SPI_FLASH_CS_HIGH();
    spi_flash_wait_for_write_end();
}

void spi_flash_buffer_write(uint8_t *pbuffer, uint32_t write_addr, uint16_t num_byte_to_write)
{
    uint8_t num_of_page = 0, num_of_single = 0, addr = 0, count = 0, temp = 0;

    addr = write_addr % SPI_FLASH_PAGE_SIZE;
    count = SPI_FLASH_PAGE_SIZE - addr;
    num_of_page = num_byte_to_write / SPI_FLASH_PAGE_SIZE;
    num_of_single = num_byte_to_write % SPI_FLASH_PAGE_SIZE;

    if (0 == addr)
    {
        if (0 == num_of_page)
        {
            spi_flash_page_write(pbuffer, write_addr, num_byte_to_write);
        }
        else
        {
            while (num_of_page--)
            {
                spi_flash_page_write(pbuffer, write_addr, SPI_FLASH_PAGE_SIZE);
                write_addr += SPI_FLASH_PAGE_SIZE;
                pbuffer += SPI_FLASH_PAGE_SIZE;
            }
            spi_flash_page_write(pbuffer, write_addr, num_of_single);
        }
    }
    else
    {
        if (0 == num_of_page)
        {
            if (num_of_single > count)
            {
                temp = num_of_single - count;
                spi_flash_page_write(pbuffer, write_addr, count);
                write_addr += count;
                pbuffer += count;
                spi_flash_page_write(pbuffer, write_addr, temp);
            }
            else
            {
                spi_flash_page_write(pbuffer, write_addr, num_byte_to_write);
            }
        }
        else
        {
            num_byte_to_write -= count;
            num_of_page = num_byte_to_write / SPI_FLASH_PAGE_SIZE;
            num_of_single = num_byte_to_write % SPI_FLASH_PAGE_SIZE;

            spi_flash_page_write(pbuffer, write_addr, count);
            write_addr += count;
            pbuffer += count;

            while (num_of_page--)
            {
                spi_flash_page_write(pbuffer, write_addr, SPI_FLASH_PAGE_SIZE);
                write_addr += SPI_FLASH_PAGE_SIZE;
                pbuffer += SPI_FLASH_PAGE_SIZE;
            }

            if (0 != num_of_single)
            {
                spi_flash_page_write(pbuffer, write_addr, num_of_single);
            }
        }
    }
}

void spi_flash_buffer_read(uint8_t *pbuffer, uint32_t read_addr, uint16_t num_byte_to_read)
{
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(READ);
    spi_flash_send_byte_dma((read_addr & 0xFF0000) >> 16);
    spi_flash_send_byte_dma((read_addr & 0xFF00) >> 8);
    spi_flash_send_byte_dma(read_addr & 0xFF);

    while (num_byte_to_read--)
    {
        *pbuffer = spi_flash_send_byte_dma(DUMMY_BYTE);
        pbuffer++;
    }

    SPI_FLASH_CS_HIGH();
}

uint32_t spi_flash_read_id(void)
{
    uint32_t temp = 0, temp0 = 0, temp1 = 0, temp2 = 0;

    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(RDID);
    temp0 = spi_flash_send_byte_dma(DUMMY_BYTE);
    temp1 = spi_flash_send_byte_dma(DUMMY_BYTE);
    temp2 = spi_flash_send_byte_dma(DUMMY_BYTE);
    SPI_FLASH_CS_HIGH();

    temp = (temp0 << 16) | (temp1 << 8) | temp2;
    return temp;
}

void spi_flash_start_read_sequence(uint32_t read_addr)
{
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(READ);
    spi_flash_send_byte_dma((read_addr & 0xFF0000) >> 16);
    spi_flash_send_byte_dma((read_addr & 0xFF00) >> 8);
    spi_flash_send_byte_dma(read_addr & 0xFF);
}

void spi_flash_write_enable(void)
{
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(WREN);
    SPI_FLASH_CS_HIGH();
}

void spi_flash_wait_for_write_end(void)
{
    uint8_t flash_status = 0;

    SPI_FLASH_CS_LOW();
    spi_flash_send_byte_dma(RDSR);

    do
    {
        flash_status = spi_flash_send_byte_dma(DUMMY_BYTE);
    } while ((flash_status & WIP_FLAG) == 0x01);

    SPI_FLASH_CS_HIGH();
}

/**
 * @brief 使用 DMA 发送并接收一个字节
 * @param byte 要发送的字节
 * @return 从 SPI 总线接收到的字节
 */
uint8_t spi_flash_send_byte_dma(uint8_t byte)
{
    /* 将数据放入发送缓冲区 */
    spi1_send_array[0] = byte;
    
    /* 配置发送 DMA，只发送一个字节 */
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 配置 DMA 发送通道 */
    dma_deinit(DMA0, DMA_CH4);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = 1; /* 只发送一个字节 */
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA0, DMA_CH4, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH4, DMA_SUBPERI0);
    
    /* 配置 DMA 接收通道 */
    dma_deinit(DMA0, DMA_CH3);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA0, DMA_CH3, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH3, DMA_SUBPERI0);
    
    /* 启用接收和发送的 DMA 通道 */
    dma_channel_enable(DMA0, DMA_CH3);
    dma_channel_enable(DMA0, DMA_CH4);
    
    /* 启用 SPI 的 DMA 接收和发送功能 */
    spi_dma_enable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_FLASH, SPI_DMA_TRANSMIT);
    
    /* 等待 DMA 传输完成 */
    while(RESET == dma_flag_get(DMA0, DMA_CH3, DMA_FLAG_FTF));
    
    /* 禁用 DMA */
    spi_dma_disable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_FLASH, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA0, DMA_CH3);
    dma_channel_disable(DMA0, DMA_CH4);
    
    /* 清除 DMA 标志 */
    dma_flag_clear(DMA0, DMA_CH3, DMA_FLAG_FTF);
    dma_flag_clear(DMA0, DMA_CH4, DMA_FLAG_FTF);
    
    /* 返回接收到的数据 */
    return spi1_receive_array[0];
}

/**
 * @brief 使用 DMA 发送并接收一个半字（16位数据）
 * @param half_word 要发送的半字
 * @return 从 SPI 总线接收到的半字
 */
uint16_t spi_flash_send_halfword_dma(uint16_t half_word)
{
    uint16_t rx_data;
    
    /* 先发送高8位 */
    spi1_send_array[0] = (uint8_t)(half_word >> 8);
    spi1_send_array[1] = (uint8_t)half_word;
    
    /* 配置 DMA 参数 */
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 配置 DMA 发送通道 */
    dma_deinit(DMA0, DMA_CH4);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = 2; /* 发送2个字节 */
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA0, DMA_CH4, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH4, DMA_SUBPERI0);
    
    /* 配置 DMA 接收通道 */
    dma_deinit(DMA0, DMA_CH3);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA0, DMA_CH3, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH3, DMA_SUBPERI0);
    
    /* 启用接收和发送的 DMA 通道 */
    dma_channel_enable(DMA0, DMA_CH3);
    dma_channel_enable(DMA0, DMA_CH4);
    
    /* 启用 SPI 的 DMA 接收和发送功能 */
    spi_dma_enable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_FLASH, SPI_DMA_TRANSMIT);
    
    /* 等待 DMA 传输完成 */
    while(RESET == dma_flag_get(DMA0, DMA_CH3, DMA_FLAG_FTF));
    
    /* 禁用 DMA */
    spi_dma_disable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_FLASH, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA0, DMA_CH3);
    dma_channel_disable(DMA0, DMA_CH4);
    
    /* 清除 DMA 标志 */
    dma_flag_clear(DMA0, DMA_CH3, DMA_FLAG_FTF);
    dma_flag_clear(DMA0, DMA_CH4, DMA_FLAG_FTF);
    
    /* 组合接收到的数据 */
    rx_data = (uint16_t)(spi1_receive_array[0] << 8);
    rx_data |= spi1_receive_array[1];
    
    return rx_data;
}

/**
 * @brief 使用 DMA 发送和接收多个字节
 * @param tx_buffer 发送缓冲区
 * @param rx_buffer 接收缓冲区
 * @param size 传输大小
 */
void spi_flash_transmit_receive_dma(uint8_t *tx_buffer, uint8_t *rx_buffer, uint16_t size)
{
    /* 检查传输大小是否超过缓冲区 */
    if (size > ARRAYSIZE) {
        size = ARRAYSIZE;
    }
    
    /* 准备发送数据 */
    for (uint16_t i = 0; i < size; i++) {
        spi1_send_array[i] = tx_buffer[i];
    }
    
    /* 配置 DMA 参数 */
    dma_single_data_parameter_struct dma_init_struct;
    
    /* 配置 DMA 发送通道 */
    dma_deinit(DMA0, DMA_CH4);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_send_array;
    dma_init_struct.direction           = DMA_MEMORY_TO_PERIPH;
    dma_init_struct.periph_memory_width = DMA_PERIPH_WIDTH_8BIT;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_init_struct.number              = size;
    dma_init_struct.periph_inc          = DMA_PERIPH_INCREASE_DISABLE;
    dma_init_struct.memory_inc          = DMA_MEMORY_INCREASE_ENABLE;
    dma_init_struct.circular_mode       = DMA_CIRCULAR_MODE_DISABLE;
    dma_single_data_mode_init(DMA0, DMA_CH4, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH4, DMA_SUBPERI0);
    
    /* 配置 DMA 接收通道 */
    dma_deinit(DMA0, DMA_CH3);
    dma_init_struct.periph_addr         = (uint32_t)&SPI_DATA(SPI_FLASH);
    dma_init_struct.memory0_addr        = (uint32_t)spi1_receive_array;
    dma_init_struct.direction           = DMA_PERIPH_TO_MEMORY;
    dma_init_struct.priority            = DMA_PRIORITY_HIGH;
    dma_single_data_mode_init(DMA0, DMA_CH3, &dma_init_struct);
    dma_channel_subperipheral_select(DMA0, DMA_CH3, DMA_SUBPERI0);
    
    /* 启用接收和发送的 DMA 通道 */
    dma_channel_enable(DMA0, DMA_CH3);
    dma_channel_enable(DMA0, DMA_CH4);
    
    /* 启用 SPI 的 DMA 接收和发送功能 */
    spi_dma_enable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_enable(SPI_FLASH, SPI_DMA_TRANSMIT);
    
    /* 等待 DMA 传输完成 */
    while(RESET == dma_flag_get(DMA0, DMA_CH3, DMA_FLAG_FTF));
    
    /* 禁用 DMA */
    spi_dma_disable(SPI_FLASH, SPI_DMA_RECEIVE);
    spi_dma_disable(SPI_FLASH, SPI_DMA_TRANSMIT);
    dma_channel_disable(DMA0, DMA_CH3);
    dma_channel_disable(DMA0, DMA_CH4);
    
    /* 清除 DMA 标志 */
    dma_flag_clear(DMA0, DMA_CH3, DMA_FLAG_FTF);
    dma_flag_clear(DMA0, DMA_CH4, DMA_FLAG_FTF);
    
    /* 复制接收到的数据到接收缓冲区 */
    for (uint16_t i = 0; i < size; i++) {
        rx_buffer[i] = spi1_receive_array[i];
    }
}

/**
 * @brief 等待 DMA 传输完成
 */
void spi_flash_wait_for_dma_end(void)
{
    /* 等待 DMA 传输完成 */
    while(RESET == dma_flag_get(DMA0, DMA_CH3, DMA_FLAG_FTF));
    
    /* 清除 DMA 标志 */
    dma_flag_clear(DMA0, DMA_CH3, DMA_FLAG_FTF);
    dma_flag_clear(DMA0, DMA_CH4, DMA_FLAG_FTF);
}



void spi_flash_buffer_erase(uint32_t sector_addr,  uint32_t num_byte_to_erase)
{
	uint8_t buffer_data[SPI_FLASH_SECTOR_SIZE] = {0};
	uint8_t buffer_data1[SPI_FLASH_SECTOR_SIZE] = {0};
	uint8_t num_of_sector = 0, num_of_single = 0, addr = 0, count = 0, temp = 0;

    addr          = sector_addr % SPI_FLASH_SECTOR_SIZE;		//扇区内地址
    count         = SPI_FLASH_PAGE_SIZE - addr;					//页内剩下可以写的空间长度
    num_of_sector = num_byte_to_erase / SPI_FLASH_SECTOR_SIZE;	//要擦除多少个满扇区空间
    num_of_single = num_byte_to_erase % SPI_FLASH_SECTOR_SIZE;	//剩下要擦除不满一个扇区的字节数
	
	/* 待擦除的地址是扇区对齐的 */
    if(0 == addr)
	{

		while(num_of_sector-- )		//擦除整数个扇区
		{
			spi_flash_sector_erase( sector_addr );
			sector_addr += SPI_FLASH_PAGE_SIZE;
		}
		if(0 != num_of_single)		//擦除小数个扇区
		{
			spi_flash_buffer_read(buffer_data, sector_addr+num_of_single, SPI_FLASH_SECTOR_SIZE-num_of_single);			//先读出扇区后部分内容
			spi_flash_sector_erase( sector_addr );		//再擦
			spi_flash_buffer_write(buffer_data, sector_addr+num_of_single, SPI_FLASH_SECTOR_SIZE-num_of_single);		//再写回扇区后部分内容	
		}
    }
	else/* 待擦除的地址是非扇区对齐的 */
	{
        if(num_byte_to_erase < count)
		{
			spi_flash_buffer_read(buffer_data, num_of_sector*SPI_FLASH_SECTOR_SIZE, addr);																	//先读出扇区前部分内容
			spi_flash_buffer_read(buffer_data1, num_of_sector*SPI_FLASH_SECTOR_SIZE+addr+num_byte_to_erase, SPI_FLASH_SECTOR_SIZE-(addr)-num_byte_to_erase);//  读出扇区后部分内容
			spi_flash_sector_erase( sector_addr );		//再擦
			spi_flash_buffer_write(buffer_data, num_of_sector*SPI_FLASH_SECTOR_SIZE, addr);																	//再写回扇区前部分内容
			spi_flash_buffer_write(buffer_data1, num_of_sector*SPI_FLASH_SECTOR_SIZE+addr+num_byte_to_erase, SPI_FLASH_SECTOR_SIZE-(addr)-num_byte_to_erase);//再写回扇区前部分内容
		}
		else
		{
			spi_flash_buffer_read(buffer_data, num_of_sector*SPI_FLASH_SECTOR_SIZE, addr);	//先读出扇区前部分内容
			spi_flash_sector_erase( sector_addr );		//再擦
			spi_flash_buffer_write(buffer_data, num_of_sector*SPI_FLASH_SECTOR_SIZE, addr);		//再写回扇区前部分内容
			
			//现在擦除地址又变得扇区对齐了
			num_byte_to_erase -= addr;
			num_of_sector = num_byte_to_erase / SPI_FLASH_SECTOR_SIZE;//要擦除多少个满扇区空间
			num_of_single = num_byte_to_erase % SPI_FLASH_SECTOR_SIZE;//剩下要擦除不满一个扇区的字节数
			sector_addr += count;
			
			while(num_of_sector-- )		//擦除整数个扇区
			{
				spi_flash_sector_erase( sector_addr );
				sector_addr += SPI_FLASH_PAGE_SIZE;
			}
			if(0 != num_of_single)		//擦除小数个扇区
			{
				spi_flash_buffer_read(buffer_data, sector_addr+num_of_single, SPI_FLASH_SECTOR_SIZE-num_of_single);			//先读出来
				spi_flash_sector_erase( sector_addr );		//再擦
				spi_flash_buffer_write(buffer_data, sector_addr+num_of_single, SPI_FLASH_SECTOR_SIZE-num_of_single);	//再写回去	
			}	
		}
    }
}

uint8_t string[30]={"this is flash test"};

void test_spi_flash(void)
{
	uint32_t flash_id = 0;
	uint8_t  tx_buffer[TX_BUFFER_SIZE];
	uint8_t  rx_buffer[RX_BUFFER_SIZE];
	uint16_t i = 0;
	uint8_t  is_successful = 0;
	
	 /* get flash id */
    flash_id = spi_flash_read_id();
	my_printf(DEBUG_USART, "Flash ID: 0x%lX\r\n", flash_id);
	
	/* flash id is correct */
    if(SFLASH_ID == flash_id)
	{
		my_printf(DEBUG_USART,"\n\r\n\r******************************erases flash sector*************************\n\r\n\r");
        /* erases the specified flash sector */
        spi_flash_sector_erase(FLASH_WRITE_ADDRESS);  //要是不想循环擦除，第一次写入后注释掉该代码
		/* 扇区擦除 */
		// （可选）验证擦除：读取一页数据并检查是否全部为0xFF
		spi_flash_buffer_read(rx_buffer,FLASH_READ_ADDRESS,RX_BUFFER_SIZE);
		int erased_check_ok = 1;  // 擦除检查标志
		for (int i = 0; i < SPI_FLASH_PAGE_SIZE; i++)
		{
			if (rx_buffer[i] != 0xFF)
			{
				erased_check_ok = 0;  // 发现非0xFF字节
				break;
			}
		}
		if (erased_check_ok)
		{
			 my_printf(DEBUG_USART,"\n\rErase check PASSED. Sector is all 0xFF");
		}
		else
		{
			 my_printf(DEBUG_USART,"\n\rErase check FAILED");
		}

		 my_printf(DEBUG_USART,"\n\r\n\r******************************Write to tx_buffer:*************************\n\r\n\r");
		/* printf tx_buffer value */
		memset(tx_buffer, 0, SPI_FLASH_PAGE_SIZE);      // 清空写入缓冲区
//        for(i = 0; i < BUFFER_SIZE; i ++)
//		 {
//            tx_buffer[i] = i;
//            printf("0x%02X ",tx_buffer[i]);

//            if(15 == i%16)
//                printf("\n\r");
//        }
		const char *message = "Hello from STM32 to SPI FLASH! Microunion Studio Test - 12345.";
		uint16_t data_len = strlen(message);  // 消息长度
		
		if (data_len >= SPI_FLASH_PAGE_SIZE)
		{
			data_len = SPI_FLASH_PAGE_SIZE - 1; // 确保不超过页大小
		}
		memcpy(tx_buffer, message, data_len);           // 复制消息到缓冲区
		tx_buffer[data_len] = '\0';                     // 确保字符串结束符
		for(i = 0; i < strlen(message); i++) 
		{
			my_printf(DEBUG_USART,"%c", tx_buffer[i]);

		}
		my_printf(DEBUG_USART,"\n\r");
        /* write tx_buffer data to the flash */ 
        spi_flash_buffer_write(tx_buffer,FLASH_WRITE_ADDRESS,TX_BUFFER_SIZE);
		
        delay_1ms(10);
		
		my_printf(DEBUG_USART,"\n\r\n\r******************************Read from tx_buffer:*************************\n\r\n\r");
		memset(rx_buffer, 0, SPI_FLASH_PAGE_SIZE);      // 清空读出缓冲区
        /* read a block of data from the flash to rx_buffer */
//		spi_flash_buffer_erase(0,20);
        spi_flash_buffer_read(rx_buffer,FLASH_READ_ADDRESS,RX_BUFFER_SIZE);
        /* printf rx_buffer value */
        for(i = 0; i < strlen(message); i++) 
		{
			my_printf(DEBUG_USART,"%c", rx_buffer[i]);

		}
		my_printf(DEBUG_USART,"\n\r");
		/*比对读出和写入的数据*/
        if(ERROR == flash_memory_compare(tx_buffer,rx_buffer,256)){
            my_printf(DEBUG_USART,"Err:Data Read and Write aren't Matching.\n\r");
            is_successful = 1;
        }
		
        /* spi flash test passed */
        if(0 == is_successful){
			gpio_bit_set(GPIOA, GPIO_PIN_6);
			delay_1ms(1);
            my_printf(DEBUG_USART,"\n\rSPI-GD25Q40ESIGR Test Passed!\n\r");
        }else{
			/* spi flash read id fail */
			my_printf(DEBUG_USART,"\n\rSPI Flash: Read ID Fail!\n\r");
		}
		
		
	
		spi_flash_buffer_erase(0,18);		//从0地址处，擦除18个长度
		spi_flash_buffer_write(string,0,18);
		spi_flash_buffer_read(rx_buffer,0,18);
		
		
		my_printf(DEBUG_USART,"String variable reading\r\n");
        /* printf rx_buffer value */
        for(i = 0; i < 18; i ++){
            my_printf(DEBUG_USART,"%c", rx_buffer[i]);
        }
	}
	
}

/*!
    \brief      memory compare function
    \param[in]  src: source data pointer
    \param[in]  dst: destination data pointer
    \param[in]  length: the compare data length
    \param[out] none
    \retval     ErrStatus: ERROR or SUCCESS
*/
ErrStatus flash_memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

/****************************End*****************************/


