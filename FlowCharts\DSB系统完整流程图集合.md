# DSB系统完整流程图集合

本文件包含DSB数据采集与存储系统的所有核心流程图，每个图都经过优化，内容简洁，字体清晰，方便截图使用。

---

## 1. 系统总体架构图

```mermaid
graph TD
    A[DSB数据采集系统] --> B[硬件层]
    A --> C[驱动层]
    A --> D[应用层]
    A --> E[用户接口层]
    
    B --> B1[GD32F470VET6<br/>微控制器]
    B --> B2[ADC采集模块]
    B --> B3[SD卡存储]
    B --> B4[OLED显示屏]
    B --> B5[按键输入]
    B --> B6[串口通信]
    
    C --> C1[BSP驱动]
    C --> C2[DMA驱动]
    C --> C3[定时器驱动]
    C --> C4[文件系统]
    
    D --> D1[任务调度器]
    D --> D2[数据处理]
    D --> D3[存储管理]
    D --> D4[通信协议]
    
    E --> E1[按键控制]
    E --> E2[OLED显示]
    E --> E3[串口命令]
    E --> E4[LED指示]
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

---

## 2. 系统初始化流程

```mermaid
graph TD
    A[系统上电] --> B[硬件初始化]
    B --> C[外设配置]
    C --> D[参数加载]
    D --> E[自检测试]
    E --> F[系统就绪]
    
    B --> B1[LED初始化]
    B --> B2[按键初始化]
    B --> B3[OLED初始化]
    B --> B4[串口初始化]
    B --> B5[ADC初始化]
    
    C --> C1[DMA配置]
    C --> C2[定时器配置]
    C --> C3[中断配置]
    
    D --> D1[Flash参数读取]
    D --> D2[变比阈值设置]
    D --> D3[采样周期设置]
    
    E --> E1[SD卡检测]
    E --> E2[Flash测试]
    E --> E3[ADC测试]
    
    style A fill:#e3f2fd
    style F fill:#c8e6c9
```

---

## 3. 任务调度器核心逻辑

```mermaid
graph TD
    A[scheduler_run开始] --> B[遍历任务列表]
    B --> C{检查执行时间}
    C -->|时间到| D[执行任务]
    C -->|时间未到| E[检查下一任务]
    D --> F[更新执行时间]
    F --> E
    E --> G{还有任务?}
    G -->|是| C
    G -->|否| A
    
    D --> D1[adc_process_task<br/>1ms周期]
    D --> D2[btn_task<br/>5ms周期]
    D --> D3[uart_task<br/>5ms周期]
    D --> D4[adc_flip_task<br/>5ms周期]
    D --> D5[rtc_task<br/>50ms周期]
    
    style A fill:#e3f2fd
    style D fill:#fff3e0
    style G fill:#fff9c4
```

---

## 4. ADC数据采集流程

```mermaid
graph TD
    A[定时器中断触发] --> B[设置采样标志]
    B --> C[ADC DMA采集]
    C --> D[主循环检查标志]
    D --> E{标志位有效?}
    E -->|是| F[读取ADC值]
    E -->|否| G[等待下次采集]
    F --> H[电压转换]
    H --> I[数据处理完成]
    
    A --> A1[TIMER6中断]
    A --> A2[周期计数判断]
    A --> A3[设置execute_flag]
    
    C --> C1[12位ADC转换]
    C --> C2[DMA自动传输]
    C --> C3[存储到adc_value数组]
    
    H --> H1[应用变比系数]
    H --> H2[计算实际电压]
    H --> H3[格式化显示]
    
    style A fill:#e3f2fd
    style I fill:#c8e6c9
    style E fill:#fff9c4
```

---

## 5. 数据处理与判断流程

```mermaid
graph TD
    A[获取电压值] --> B[超限检测]
    B --> C{电压 > 阈值?}
    C -->|是| D[超限处理]
    C -->|否| E[正常处理]
    
    D --> D1[点亮LED2]
    D --> D2[显示超限信息]
    D --> D3[存储超限数据]
    
    E --> E1[关闭LED2]
    E --> E2[显示正常信息]
    E --> E3[存储正常数据]
    
    D3 --> F[OLED显示更新]
    E3 --> F
    F --> G[串口输出]
    G --> H[处理完成]
    
    style A fill:#e3f2fd
    style C fill:#fff9c4
    style D fill:#ffebee
    style E fill:#e8f5e8
    style H fill:#c8e6c9
```

---

## 6. 数据存储分类流程

```mermaid
graph TD
    A[数据存储请求] --> B{数据类型}
    B -->|正常数据| C[sample文件夹]
    B -->|超限数据| D[overLimit文件夹]
    B -->|加密数据| E[hideData文件夹]
    B -->|系统日志| F[log文件夹]
    
    C --> G[检查文件记录数]
    D --> G
    E --> G
    F --> G
    
    G --> H{记录数 >= 10?}
    H -->|是| I[创建新文件]
    H -->|否| J[使用当前文件]
    
    I --> K[时间戳命名]
    K --> L[写入数据]
    J --> L
    L --> M[同步文件系统]
    M --> N[存储完成]
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style H fill:#fff9c4
    style N fill:#c8e6c9
```

---

## 7. 串口命令处理流程

```mermaid
graph TD
    A[串口数据接收] --> B[环形缓冲区]
    B --> C[命令解析]
    C --> D{命令类型}

    D -->|START| E[启动采样]
    D -->|STOP| F[停止采样]
    D -->|SET_TIME| G[设置时间]
    D -->|SET_RADIO| H[设置变比]
    D -->|SET_THRESHOLD| I[设置阈值]
    D -->|GET_STATUS| J[查询状态]

    E --> K[启动定时器]
    F --> L[停止定时器]
    G --> M[更新RTC]
    H --> N[保存变比参数]
    I --> O[保存阈值参数]
    J --> P[输出系统状态]

    K --> Q[命令执行完成]
    L --> Q
    M --> Q
    N --> Q
    O --> Q
    P --> Q

    style A fill:#e3f2fd
    style D fill:#f3e5f5
    style Q fill:#c8e6c9
```

---

## 8. 按键处理流程

```mermaid
graph TD
    A[按键扫描] --> B{按键状态}
    B -->|KEY3按下| C[采样控制切换]
    B -->|KEY4按下| D[设置5秒周期]
    B -->|KEY5按下| E[设置15秒周期]
    B -->|KEY6按下| F[设置10秒周期]
    B -->|无按键| G[返回等待]

    C --> H[切换采样状态]
    D --> I[保存周期参数]
    E --> I
    F --> I

    H --> J{当前状态}
    J -->|停止| K[启动采样]
    J -->|运行| L[停止采样]

    I --> M[Flash参数保存]
    K --> N[更新显示状态]
    L --> N
    M --> N
    N --> O[按键处理完成]

    style A fill:#e3f2fd
    style B fill:#fff9c4
    style J fill:#fff9c4
    style O fill:#c8e6c9
```

---

## 9. 定时器中断处理

```mermaid
graph TD
    A[TIMER6中断] --> B[清除中断标志]
    B --> C[周期计数器++]
    C --> D{采样周期判断}

    D -->|5秒周期| E[每次都执行]
    D -->|10秒周期| F{计数器>=2?}
    D -->|15秒周期| G{计数器>=3?}

    E --> H[设置执行标志]
    F -->|是| I[设置执行标志]
    F -->|否| J[继续计数]
    G -->|是| K[设置执行标志]
    G -->|否| L[继续计数]

    H --> M[重置计数器]
    I --> M
    K --> M
    J --> N[退出中断]
    L --> N
    M --> O[timer6_execute_flag=1]
    O --> N

    style A fill:#e3f2fd
    style D fill:#f3e5f5
    style F fill:#fff9c4
    style G fill:#fff9c4
    style N fill:#c8e6c9
```

---

## 10. 文件系统操作流程

```mermaid
graph TD
    A[文件操作请求] --> B{操作类型}
    B -->|创建文件| C[生成文件名]
    B -->|写入数据| D[打开文件]
    B -->|读取数据| E[定位文件]

    C --> F[检查文件夹]
    F --> G{文件夹存在?}
    G -->|否| H[创建文件夹]
    G -->|是| I[创建文件]
    H --> I

    D --> J[格式化数据]
    J --> K[写入文件]
    K --> L[同步文件系统]

    E --> M[读取文件内容]
    M --> N[解析数据]

    I --> O[文件操作完成]
    L --> O
    N --> O

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style G fill:#fff9c4
    style O fill:#c8e6c9
```

---

## 11. 加密数据处理流程

```mermaid
graph TD
    A[加密模式启用] --> B[获取时间戳]
    B --> C[时间戳转HEX]
    C --> D[电压值转HEX]
    D --> E[组合加密数据]
    E --> F{是否超限?}
    F -->|是| G[添加超限标记*]
    F -->|否| H[正常加密数据]

    G --> I[输出HEX数据*]
    H --> J[输出HEX数据]

    I --> K[存储到hideData]
    J --> K
    K --> L[加密处理完成]

    style A fill:#e3f2fd
    style F fill:#fff9c4
    style L fill:#c8e6c9
```

---

## 12. 系统状态监控流程

```mermaid
graph TD
    A[系统监控启动] --> B[检查SD卡状态]
    B --> C[检查Flash状态]
    C --> D[检查ADC状态]
    D --> E[检查串口状态]
    E --> F[检查定时器状态]

    F --> G{发现异常?}
    G -->|是| H[记录错误日志]
    G -->|否| I[系统正常]

    H --> J{错误级别}
    J -->|轻微| K[继续监控]
    J -->|严重| L[系统重启]

    I --> M[更新状态显示]
    K --> M
    M --> N[监控周期完成]

    style A fill:#e3f2fd
    style G fill:#fff9c4
    style J fill:#fff9c4
    style L fill:#ffebee
    style N fill:#c8e6c9
```
```
