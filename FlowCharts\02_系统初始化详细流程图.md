# 系统初始化详细流程图

## 图表说明
本流程图详细展示了main.c中系统初始化的完整过程，包括硬件初始化、参数加载、自检测试等关键步骤。

## Mermaid代码

```mermaid
graph TD
    A[main函数开始] --> B[LED初始化<br/>bsp_led_init]
    B --> C[按键初始化<br/>bsp_btn_init]
    C --> D[OLED初始化<br/>bsp_oled_init]
    D --> E[Flash初始化<br/>bsp_gd25qxx_init]
    E --> F[串口初始化<br/>bsp_usart_init]
    F --> G[ADC初始化<br/>bsp_adc_init]
    G --> H[SD卡文件系统初始化<br/>sd_fatfs_init]
    H --> I[OLED显示初始化<br/>OLED_Init]
    
    I --> J[Flash参数读取<br/>flash_stored]
    J --> K[变比阈值初始化<br/>flash_ratio_threshold_init]
    K --> L[开机计数初始化<br/>power_on_count_init]
    L --> M[任务调度器初始化<br/>scheduler_init]
    M --> N[定时器初始化<br/>all_timer_init]
    
    N --> O[打印系统初始化信息]
    O --> P[记录系统初始化日志<br/>store_log_entry]
    P --> Q[打印设备ID]
    Q --> R[读取Flash设备ID<br/>flash_read_direct]
    R --> S[记录硬件测试日志]
    
    S --> T{SD卡初始化状态检查}
    T -->|成功| U[记录测试成功日志<br/>test ok]
    T -->|失败| V[记录测试失败日志<br/>test error: tf card not found]
    U --> W[打印系统就绪信息]
    V --> W
    
    W --> X[进入主循环<br/>while(1)]
    X --> Y[调用任务调度器<br/>scheduler_run]
    Y --> Y
    
    style A fill:#e3f2fd
    style X fill:#c8e6c9
    style Y fill:#fff3e0
    style T fill:#fff9c4
```

## 初始化步骤详解

### 硬件初始化阶段（步骤B-I）
1. **LED初始化**：配置LED GPIO引脚
2. **按键初始化**：配置按键GPIO和中断
3. **OLED初始化**：配置I2C接口和OLED显示屏
4. **Flash初始化**：配置SPI Flash存储器
5. **串口初始化**：配置UART通信接口
6. **ADC初始化**：配置ADC和DMA
7. **SD卡初始化**：初始化FAT文件系统
8. **OLED显示初始化**：显示屏参数配置

### 参数配置阶段（步骤J-N）
1. **Flash参数读取**：从Flash读取保存的配置参数
2. **变比阈值初始化**：设置ADC转换参数
3. **开机计数初始化**：记录系统启动次数
4. **任务调度器初始化**：计算任务数量，初始化调度器
5. **定时器初始化**：配置系统定时器

### 系统自检阶段（步骤O-W）
1. **打印初始化信息**：输出"====system init===="
2. **记录初始化日志**：调用store_log_entry("system init")
3. **设备ID输出**：显示设备唯一标识
4. **硬件测试记录**：记录硬件测试结果
5. **SD卡状态检查**：验证存储系统是否正常
6. **系统就绪提示**：输出"====system ready===="

## 关键代码对应

```c
// 对应main.c中的初始化代码
int main(void)
{
    bsp_led_init();           // 步骤B
    bsp_btn_init();           // 步骤C
    bsp_oled_init();          // 步骤D
    bsp_gd25qxx_init();       // 步骤E
    bsp_usart_init();         // 步骤F
    bsp_adc_init();           // 步骤G
    sd_fatfs_init();          // 步骤H
    OLED_Init();              // 步骤I
    
    flash_stored();           // 步骤J
    flash_ratio_threshold_init(); // 步骤K
    power_on_count_init();    // 步骤L
    scheduler_init();         // 步骤M
    all_timer_init();         // 步骤N
    
    my_printf(DEBUG_USART, "====system init====\n");  // 步骤O
    store_log_entry("system init");                    // 步骤P
    
    // 步骤Q-S：设备ID和硬件测试
    // 步骤T-W：SD卡检查和系统就绪
    
    while(1) {                // 步骤X
        scheduler_run();      // 步骤Y
    }
}
```

## 设计要点

1. **顺序初始化**：按照依赖关系顺序初始化各模块
2. **错误检测**：每个初始化步骤都有相应的状态检查
3. **日志记录**：关键步骤都有日志记录，便于调试
4. **状态反馈**：通过串口输出初始化进度信息
