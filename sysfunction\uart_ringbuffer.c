#include "uart_ringbuffer.h"
#include <string.h>

// 定义环形缓冲区结构体和缓冲区
static struct rt_ringbuffer uart_rx_rb;
static uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];

// 定义DMA接收缓冲区
uint8_t dma_rx_buffer[512];

// 初始化UART环形缓冲区
void uart_ringbuffer_init(void)
{
    // 初始化环形缓冲区
    rt_ringbuffer_init(&uart_rx_rb, uart_rx_buffer, UART_RX_BUFFER_SIZE);
    
    // 清空DMA接收缓冲区
    memset(dma_rx_buffer, 0, sizeof(dma_rx_buffer));
}

// 从环形缓冲区读取数据
uint32_t uart_ringbuffer_read(uint8_t *buffer, uint32_t size)
{
    return rt_ringbuffer_get(&uart_rx_rb, buffer, size);
}

// 获取环形缓冲区中可读数据长度
uint32_t uart_ringbuffer_available(void)
{
    return rt_ringbuffer_data_len(&uart_rx_rb);
}

// UART接收中断处理函数（在中断中调用）
void uart_ringbuffer_irq_handler(void)
{
    if(SET == usart_interrupt_flag_get(USART0, USART_INT_FLAG_IDLE))
    {
        // 清除IDLE标志
        usart_data_receive(USART0);
        
        // 计算接收到的数据长度
        uint32_t received = 512 - dma_transfer_number_get(DMA1, DMA_CH2);
        
        if(received > 0)
        {
            // 将DMA接收到的数据放入环形缓冲区
            rt_ringbuffer_put(&uart_rx_rb, dma_rx_buffer, received);
        }
        
        // 禁用DMA通道
        dma_channel_disable(DMA1, DMA_CH2);
        
        // 清除DMA标志
        dma_flag_clear(DMA1, DMA_CH2, DMA_FLAG_FTF);
        
        // 重新配置DMA
        dma_transfer_number_config(DMA1, DMA_CH2, 512);
        
        // 重新设置DMA内存地址（确保指向我们的DMA缓冲区）
        DMA_CHM0ADDR(DMA1, DMA_CH2) = (uint32_t)dma_rx_buffer;
        
        // 重新启用DMA通道
        dma_channel_enable(DMA1, DMA_CH2);
    }
}