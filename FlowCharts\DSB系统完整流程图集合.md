# DSB系统完整流程图集合

本文件包含DSB数据采集与存储系统的所有核心流程图，每个图都经过优化，内容简洁，字体清晰，方便截图使用。

---

## 1. 系统总体架构图

```mermaid
graph LR
    A[DSB数据采集系统] --> B[硬件层]
    A --> C[应用层]
    A --> D[用户接口层]

    B --> B1[GD32F470<br/>微控制器]
    B --> B2[ADC采集]
    B --> B3[SD卡存储]

    C --> C1[任务调度器]
    C --> C2[数据处理]
    C --> C3[存储管理]

    D --> D1[按键控制]
    D --> D2[OLED显示]
    D --> D3[串口命令]

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style C fill:#fff3e0
    style D fill:#fce4ec
```

---

## 2. 系统初始化流程

```mermaid
graph LR
    A[系统上电] --> B[硬件初始化]
    B --> C[参数加载]
    C --> D[自检测试]
    D --> E[系统就绪]

    B --> B1[LED/按键/OLED<br/>串口/ADC初始化]
    C --> C1[Flash参数读取<br/>变比阈值设置]
    D --> D1[SD卡/Flash<br/>ADC测试]

    style A fill:#e3f2fd
    style E fill:#c8e6c9
```

---

## 3. 任务调度器核心逻辑

```mermaid
graph LR
    A[scheduler_run] --> B{检查执行时间}
    B -->|时间到| C[执行任务]
    B -->|时间未到| D[下一任务]
    C --> E[更新时间]
    E --> D
    D --> F{还有任务?}
    F -->|是| B
    F -->|否| A

    style A fill:#e3f2fd
    style B fill:#fff9c4
    style F fill:#fff9c4
```

---

## 4. ADC数据采集流程

```mermaid
graph LR
    A[定时器中断] --> B[设置采样标志]
    B --> C[主循环检查]
    C --> D{标志位有效?}
    D -->|是| E[读取ADC值]
    D -->|否| F[等待下次]
    E --> G[电压转换]
    G --> H[处理完成]

    style A fill:#e3f2fd
    style D fill:#fff9c4
    style H fill:#c8e6c9
```

---

## 5. 数据处理与判断流程

```mermaid
graph LR
    A[获取电压值] --> B{电压>阈值?}
    B -->|是| C[超限处理]
    B -->|否| D[正常处理]
    C --> E[点亮LED2<br/>存储超限数据]
    D --> F[关闭LED2<br/>存储正常数据]
    E --> G[显示更新]
    F --> G
    G --> H[处理完成]

    style A fill:#e3f2fd
    style B fill:#fff9c4
    style C fill:#ffebee
    style D fill:#e8f5e8
    style H fill:#c8e6c9
```

---

## 6. 数据存储分类流程

```mermaid
graph LR
    A[数据存储] --> B{数据类型}
    B -->|正常| C[sample文件夹]
    B -->|超限| D[overLimit文件夹]
    B -->|加密| E[hideData文件夹]
    B -->|日志| F[log文件夹]

    C --> G[写入文件]
    D --> G
    E --> G
    F --> G
    G --> H[存储完成]

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style H fill:#c8e6c9
```

---

## 7. 串口命令处理流程

```mermaid
graph LR
    A[串口接收] --> B[命令解析]
    B --> C{命令类型}
    C -->|START| D[启动采样]
    C -->|STOP| E[停止采样]
    C -->|SET_*| F[参数设置]
    C -->|GET_STATUS| G[状态查询]

    D --> H[执行完成]
    E --> H
    F --> H
    G --> H

    style A fill:#e3f2fd
    style C fill:#f3e5f5
    style H fill:#c8e6c9
```

---

## 8. 按键处理流程

```mermaid
graph LR
    A[按键扫描] --> B{按键类型}
    B -->|KEY3| C[采样控制]
    B -->|KEY4| D[5秒周期]
    B -->|KEY5| E[15秒周期]
    B -->|KEY6| F[10秒周期]

    C --> G[切换状态]
    D --> H[保存参数]
    E --> H
    F --> H
    G --> I[处理完成]
    H --> I

    style A fill:#e3f2fd
    style B fill:#fff9c4
    style I fill:#c8e6c9
```

---

## 9. 定时器中断处理

```mermaid
graph LR
    A[TIMER6中断] --> B[计数器++]
    B --> C{周期判断}
    C -->|5秒| D[设置标志]
    C -->|10秒| E{计数>=2?}
    C -->|15秒| F{计数>=3?}

    E -->|是| D
    F -->|是| D
    E -->|否| G[继续计数]
    F -->|否| G
    D --> H[execute_flag=1]
    H --> I[退出中断]
    G --> I

    style A fill:#e3f2fd
    style C fill:#f3e5f5
    style E fill:#fff9c4
    style F fill:#fff9c4
    style I fill:#c8e6c9
```

---

## 10. 文件系统操作流程

```mermaid
graph TD
    A[数据写入请求] --> B[检查记录数]
    B --> C{记录数>=10?}
    C -->|是| D[创建新文件]
    C -->|否| E[使用当前文件]

    D --> F[时间戳命名]
    F --> G[写入数据]
    E --> G

    G --> H[同步文件系统]
    H --> I[操作完成]

    style A fill:#e3f2fd
    style C fill:#fff9c4
    style I fill:#c8e6c9
```

---

## 11. 加密数据处理流程

```mermaid
graph LR
    A[加密模式] --> B[获取时间戳]
    B --> C[转换HEX格式]
    C --> D{是否超限?}
    D -->|是| E[HEX数据+*]
    D -->|否| F[HEX数据]
    E --> G[存储hideData]
    F --> G
    G --> H[处理完成]

    style A fill:#e3f2fd
    style D fill:#fff9c4
    style H fill:#c8e6c9
```

---

## 12. 系统状态监控流程

```mermaid
graph LR
    A[系统监控] --> B[状态检查]
    B --> C{发现异常?}
    C -->|是| D[记录日志]
    C -->|否| E[系统正常]
    D --> F{错误级别}
    F -->|轻微| G[继续监控]
    F -->|严重| H[系统重启]
    E --> I[更新显示]
    G --> I
    I --> J[监控完成]

    style A fill:#e3f2fd
    style C fill:#fff9c4
    style F fill:#fff9c4
    style H fill:#ffebee
    style J fill:#c8e6c9
```
```
