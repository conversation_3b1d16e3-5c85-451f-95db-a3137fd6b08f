
#include "bsp_gd32f470vet6.h"
#include "adc_app.h"

// 全局变量，用于存储任务数量
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint8_t priority;           // 任务优先级 (0=最高优先级)
    uint32_t max_exec_time;     // 最大允许执行时间(ms)
    uint32_t missed_count;      // 错过执行次数统计
} task_t;

// 静态任务数组，包含优先级和执行时间限制
// 格式：{函数指针, 周期ms, 上次运行时间, 优先级, 最大执行时间ms, 错过次数}
static task_t scheduler_task[] =
{
    {adc_process_task, 1,  0, 0, 2,  0},   // 最高优先级，最大2ms执行时间
    {btn_task,         5,  0, 1, 3,  0},   // 高优先级，最大3ms执行时间
    {adc_flip_task,    5,  0, 1, 3,  0},   // 高优先级，最大3ms执行时间
    {uart_task,        5,  0, 2, 5,  0},   // 中优先级，最大5ms执行时间
    {rtc_task,        50,  0, 3, 10, 0},   // 低优先级，最大10ms执行时间
};

/**
 * @brief 任务调度初始化函数
 * 计算任务数组元素个数，并将结果存储到 task_num 中
 */
void scheduler_init(void)
{
    // 计算任务数组元素个数，并将结果存储到 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

/**
 * @brief 任务调度运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经达到任务执行周期，则执行该任务并更新上次运行时间
 */
// 改进的调度器：兼顾性能和安全性
void scheduler_run(void)
{
    static uint32_t last_scheduler_time = 0;
    uint32_t current_time = get_system_ms();

    // 防止时间回绕和异常情况
    if (current_time < last_scheduler_time) {
        last_scheduler_time = current_time;
        return;
    }

    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 每次循环重新获取时间，确保时间准确性
        uint32_t now_time = get_system_ms();

        // 检查当前时间是否达到任务执行时间
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
        {
            // 记录任务开始时间（用于性能监控）
            uint32_t task_start_time = now_time;

            // 更新任务上次运行时间为当前时间
            scheduler_task[i].last_run = now_time;

            // 执行任务函数
            scheduler_task[i].task_func();

            // 任务执行完成后的时间检查
            uint32_t task_end_time = get_system_ms();
            uint32_t execution_time = task_end_time - task_start_time;

            // 如果任务执行时间过长，发出警告
            if (execution_time > scheduler_task[i].rate_ms / 2) {
                // 任务执行时间超过其周期的一半，可能影响实时性
                my_printf(DEBUG_USART, "Warning: Task %d execution time %lums exceeds threshold\r\n",
                         i, execution_time);
            }

            // 如果任务执行时间过长，跳出循环，让其他任务有机会执行
            if (execution_time > 10) { // 超过10ms就跳出
                break;
            }
        }
    }

    last_scheduler_time = current_time;
}


