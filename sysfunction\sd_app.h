#ifndef __SD_APP_H_
#define __SD_APP_H_

#include "stdint.h"

// 全局变量声明
extern uint8_t sd_init_success;  // SD卡初始化状态
extern uint8_t encrypt_mode_enabled;  // 加密模式状态
extern uint16_t global_data_count;  // 全局数据计数器

void sd_fatfs_init(void);
void sd_fatfs_test(void);

void test_SD(void);

// 存储采样数据函数
void store_sample_data(float voltage);

// 存储超限数据函数
void store_overlimit_data(float voltage);

// 存储日志函数
void store_log_entry(const char *action);

// 存储加密数据函数
void store_hide_data(uint32_t timestamp, float voltage);

// 同步配置到SD卡
void sd_sync_config(void);

// 电压编码函数
void encode_voltage(float voltage, uint8_t *buffer);

// BCD转换函数
uint8_t bcd_to_decimal(uint8_t bcd_value);

// 重置数据存储系统
void reset_data_storage_system(void);

// 处理加密模式切换
void handle_encryption_mode_change(void);

void read_config_file(void);
#endif /* __SD_APP_H_ */
