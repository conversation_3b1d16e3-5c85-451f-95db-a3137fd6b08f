#include "bsp_gd32f470vet6.h"
#include "adc_app.h"

// 高级调度器：时间片轮转 + 优先级调度
#define MAX_TIME_SLICE_MS    5    // 最大时间片5ms
#define SCHEDULER_OVERHEAD   1    // 调度器开销1ms

// 全局变量，用于存储任务数量
uint8_t task_num;

typedef struct {
    void (*task_func)(void);
    uint32_t rate_ms;
    uint32_t last_run;
    uint8_t priority;           // 任务优先级 (0=最高优先级)
    uint32_t max_exec_time;     // 最大允许执行时间(ms)
    uint32_t missed_count;      // 错过执行次数统计
    uint8_t is_running;         // 任务运行状态
} task_t;

// 任务数组（从scheduler.c复制）
extern task_t scheduler_task[];

// 时间片轮转调度器
void scheduler_run_time_slice(void)
{
    static uint8_t current_task_index = 0;
    static uint32_t slice_start_time = 0;
    uint32_t current_time = get_system_ms();
    
    // 检查当前时间片是否用完
    if (current_time - slice_start_time >= MAX_TIME_SLICE_MS) {
        // 时间片用完，切换到下一个任务
        current_task_index = (current_task_index + 1) % task_num;
        slice_start_time = current_time;
    }
    
    // 从当前任务开始，查找需要执行的任务
    for (uint8_t i = 0; i < task_num; i++) {
        uint8_t task_index = (current_task_index + i) % task_num;
        task_t *task = &scheduler_task[task_index];
        
        // 检查任务是否需要执行
        if (current_time >= task->rate_ms + task->last_run) {
            uint32_t task_start_time = get_system_ms();
            
            // 标记任务开始运行
            task->is_running = 1;
            task->last_run = task_start_time;
            
            // 执行任务
            task->task_func();
            
            // 标记任务结束
            task->is_running = 0;
            
            uint32_t task_end_time = get_system_ms();
            uint32_t execution_time = task_end_time - task_start_time;
            
            // 检查执行时间是否超限
            if (execution_time > task->max_exec_time) {
                my_printf(DEBUG_USART, "Task %d overtime: %lums (limit: %lums)\r\n", 
                         task_index, execution_time, task->max_exec_time);
            }
            
            // 更新当前任务索引和时间片
            current_task_index = task_index;
            slice_start_time = task_end_time;
            
            // 执行一个任务后退出，给其他任务机会
            break;
        }
    }
}

// 优先级调度器（紧急任务优先）
void scheduler_run_priority(void)
{
    uint32_t current_time = get_system_ms();
    
    // 按优先级排序执行任务
    for (uint8_t priority = 0; priority <= 3; priority++) {
        for (uint8_t i = 0; i < task_num; i++) {
            task_t *task = &scheduler_task[i];
            
            // 只执行当前优先级的任务
            if (task->priority != priority) {
                continue;
            }
            
            // 检查任务是否需要执行
            if (current_time >= task->rate_ms + task->last_run) {
                uint32_t task_start_time = get_system_ms();
                
                task->is_running = 1;
                task->last_run = task_start_time;
                
                // 执行任务
                task->task_func();
                
                task->is_running = 0;
                
                uint32_t task_end_time = get_system_ms();
                uint32_t execution_time = task_end_time - task_start_time;
                
                // 超时检查
                if (execution_time > task->max_exec_time) {
                    my_printf(DEBUG_USART, "Priority task %d overtime: %lums\r\n", 
                             i, execution_time);
                }
                
                // 高优先级任务执行后立即返回，确保实时性
                if (priority <= 1) {
                    return;
                }
            }
        }
    }
}

// 混合调度器：结合优先级和时间片
void scheduler_run_hybrid(void)
{
    static uint32_t last_schedule_time = 0;
    uint32_t current_time = get_system_ms();
    
    // 防止调度过于频繁
    if (current_time - last_schedule_time < 1) {
        return;
    }
    
    // 首先执行高优先级任务（0-1级）
    for (uint8_t i = 0; i < task_num; i++) {
        task_t *task = &scheduler_task[i];
        
        if (task->priority <= 1 && 
            current_time >= task->rate_ms + task->last_run) {
            
            uint32_t task_start_time = get_system_ms();
            task->last_run = task_start_time;
            task->is_running = 1;
            
            task->task_func();
            
            task->is_running = 0;
            uint32_t execution_time = get_system_ms() - task_start_time;
            
            if (execution_time > task->max_exec_time) {
                task->missed_count++;
            }
            
            last_schedule_time = get_system_ms();
            return; // 高优先级任务执行后立即返回
        }
    }
    
    // 然后使用时间片轮转执行低优先级任务
    scheduler_run_time_slice();
    last_schedule_time = current_time;
}

// 获取调度器统计信息
void scheduler_get_stats(void)
{
    my_printf(DEBUG_USART, "\r\n=== Scheduler Statistics ===\r\n");
    
    for (uint8_t i = 0; i < task_num; i++) {
        task_t *task = &scheduler_task[i];
        my_printf(DEBUG_USART, "Task %d: Priority=%d, Missed=%lu, Running=%d\r\n",
                 i, task->priority, task->missed_count, task->is_running);
    }
    
    my_printf(DEBUG_USART, "============================\r\n");
}

// 检测调度器死锁
uint8_t scheduler_deadlock_check(void)
{
    static uint32_t last_check_time = 0;
    static uint8_t last_running_tasks = 0;
    uint32_t current_time = get_system_ms();
    
    // 每1秒检查一次
    if (current_time - last_check_time >= 1000) {
        uint8_t running_tasks = 0;
        
        // 统计正在运行的任务数
        for (uint8_t i = 0; i < task_num; i++) {
            if (scheduler_task[i].is_running) {
                running_tasks++;
            }
        }
        
        // 如果有任务一直在运行，可能发生死锁
        if (running_tasks > 0 && running_tasks == last_running_tasks) {
            my_printf(DEBUG_USART, "Warning: Possible deadlock detected!\r\n");
            return 1; // 检测到死锁
        }
        
        last_running_tasks = running_tasks;
        last_check_time = current_time;
    }
    
    return 0; // 正常
}
