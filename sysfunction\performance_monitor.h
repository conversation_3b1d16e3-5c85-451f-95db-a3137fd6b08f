#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 性能监控结构体
typedef struct {
    uint32_t task_run_count;        // 任务运行次数
    uint32_t max_execution_time;    // 最大执行时间(us)
    uint32_t total_execution_time;  // 总执行时间(us)
    uint32_t avg_execution_time;    // 平均执行时间(us)
    uint32_t last_execution_time;   // 上次执行时间(us)
} task_performance_t;

// 系统性能监控结构体
typedef struct {
    uint32_t cpu_usage_percent;     // CPU使用率(%)
    uint32_t free_stack_size;       // 剩余栈空间
    uint32_t heap_usage;            // 堆使用量
    uint32_t scheduler_overhead;    // 调度器开销(us)
} system_performance_t;

// 性能监控函数
void performance_monitor_init(void);
void performance_task_start(uint8_t task_id);
void performance_task_end(uint8_t task_id);
void performance_get_system_stats(system_performance_t *stats);
void performance_print_report(void);

// 性能监控宏
#define PERF_START(task_id) performance_task_start(task_id)
#define PERF_END(task_id)   performance_task_end(task_id)

#ifdef __cplusplus
}
#endif

#endif /* PERFORMANCE_MONITOR_H */
