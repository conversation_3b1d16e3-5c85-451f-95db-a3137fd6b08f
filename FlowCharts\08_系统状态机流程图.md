# DSB系统状态机流程图

## 图表说明
本状态机图展示了DSB系统的各种运行状态和状态转换逻辑，包括正常运行流程和异常处理机制。

## Mermaid代码

```mermaid
stateDiagram-v2
    [*] --> 系统初始化
    
    系统初始化 --> 硬件初始化
    硬件初始化 --> 参数加载
    参数加载 --> 自检测试
    自检测试 --> 系统就绪
    
    系统就绪 --> 空闲状态
    
    空闲状态 --> 采样运行 : 按键/串口启动
    空闲状态 --> 参数设置 : 按键/串口设置
    空闲状态 --> 状态查询 : 串口查询
    
    采样运行 --> 数据采集
    数据采集 --> 数据处理
    数据处理 --> 超限检测
    超限检测 --> 正常数据 : 电压正常
    超限检测 --> 超限数据 : 电压超限
    
    正常数据 --> 数据存储
    超限数据 --> 超限处理
    超限处理 --> 数据存储
    
    数据存储 --> 显示更新
    显示更新 --> 采样运行 : 继续采样
    显示更新 --> 空闲状态 : 停止采样
    
    参数设置 --> 变比设置 : SET_RADIO
    参数设置 --> 阈值设置 : SET_THRESHOLD
    参数设置 --> 时间设置 : SET_TIME
    参数设置 --> 周期设置 : 按键切换
    
    变比设置 --> 参数保存
    阈值设置 --> 参数保存
    时间设置 --> 参数保存
    周期设置 --> 参数保存
    
    参数保存 --> 空闲状态
    
    状态查询 --> 状态输出
    状态输出 --> 空闲状态
    
    采样运行 --> 错误处理 : 系统异常
    数据处理 --> 错误处理 : 处理异常
    数据存储 --> 错误处理 : 存储异常
    
    错误处理 --> 错误记录
    错误记录 --> 空闲状态 : 可恢复错误
    错误记录 --> 系统重启 : 严重错误
    
    系统重启 --> [*]
    
    note right of 采样运行
        定时器中断触发
        ADC DMA采集
        1ms/5ms/50ms任务调度
    end note
    
    note right of 数据存储
        sample文件夹：正常数据
        overLimit文件夹：超限数据
        hideData文件夹：加密数据
        log文件夹：系统日志
    end note
```

## 状态机详解

### 系统启动状态

#### 系统初始化
- **触发条件**：系统上电或复位
- **主要任务**：基础系统初始化
- **持续时间**：约100ms
- **下一状态**：硬件初始化

#### 硬件初始化
- **主要任务**：所有硬件模块初始化
- **包含模块**：LED、按键、OLED、Flash、串口、ADC、SD卡
- **持续时间**：约500ms
- **下一状态**：参数加载

#### 参数加载
- **主要任务**：从Flash读取配置参数
- **加载内容**：变比、阈值、采样周期等
- **持续时间**：约50ms
- **下一状态**：自检测试

#### 自检测试
- **主要任务**：系统硬件功能测试
- **测试项目**：SD卡、Flash、ADC、串口等
- **持续时间**：约200ms
- **下一状态**：系统就绪

### 运行状态

#### 空闲状态
- **系统状态**：等待用户指令
- **主要任务**：
  - 监听按键输入
  - 监听串口命令
  - 维持基本显示
  - 执行后台任务
- **状态转换**：
  - 按键/串口启动 → 采样运行
  - 按键/串口设置 → 参数设置
  - 串口查询 → 状态查询

#### 采样运行状态
- **系统状态**：正在进行数据采集
- **主要任务**：
  - 定时器中断触发采样
  - ADC DMA自动采集
  - 任务调度器协调各任务
- **子状态循环**：
  ```
  数据采集 → 数据处理 → 超限检测 → 数据存储 → 显示更新
  ```
- **状态转换**：
  - 继续采样 → 保持采样运行
  - 停止命令 → 空闲状态

### 数据处理状态

#### 数据采集
- **触发条件**：timer6_execute_flag = 1
- **主要任务**：读取ADC DMA缓冲区数据
- **持续时间**：<1ms
- **下一状态**：数据处理

#### 数据处理
- **主要任务**：
  - ADC原始值转换为电压值
  - 应用变比系数
  - 格式化数据
- **持续时间**：<5ms
- **下一状态**：超限检测

#### 超限检测
- **判断条件**：Vol_Value > input_threshold
- **分支状态**：
  - 电压正常 → 正常数据处理
  - 电压超限 → 超限数据处理

#### 正常数据处理
- **主要任务**：
  - 关闭超限LED
  - 显示正常状态
  - 准备正常数据存储
- **下一状态**：数据存储

#### 超限处理
- **主要任务**：
  - 点亮超限LED
  - 显示超限警告
  - 准备超限数据存储
- **下一状态**：数据存储

### 参数设置状态

#### 参数设置入口
- **触发方式**：
  - 按键操作（KEY4/KEY5/KEY6）
  - 串口命令（SET_RADIO/SET_THRESHOLD/SET_TIME）
- **分支状态**：
  - SET_RADIO → 变比设置
  - SET_THRESHOLD → 阈值设置
  - SET_TIME → 时间设置
  - 按键切换 → 周期设置

#### 参数保存
- **主要任务**：
  - 验证参数有效性
  - 保存参数到Flash
  - 更新全局变量
  - 记录操作日志
- **下一状态**：空闲状态

### 错误处理状态

#### 错误检测
- **检测范围**：
  - 系统异常（看门狗、栈溢出）
  - 处理异常（数据错误、计算异常）
  - 存储异常（SD卡错误、Flash错误）

#### 错误分类
```c
typedef enum {
    ERROR_RECOVERABLE,    // 可恢复错误
    ERROR_CRITICAL,       // 严重错误
    ERROR_FATAL          // 致命错误
} error_level_t;
```

#### 错误处理策略
- **可恢复错误**：记录日志，返回空闲状态
- **严重错误**：尝试恢复，多次失败后重启
- **致命错误**：立即重启系统

## 状态转换触发条件

### 用户触发
- **按键操作**：
  - KEY3：采样启动/停止
  - KEY4：设置5秒周期
  - KEY5：设置15秒周期
  - KEY6：设置10秒周期

- **串口命令**：
  - START/STOP：采样控制
  - SET_*：参数设置
  - GET_STATUS：状态查询

### 系统触发
- **定时器中断**：触发数据采集
- **数据就绪**：触发数据处理
- **错误检测**：触发错误处理
- **看门狗超时**：触发系统重启

### 外部触发
- **电源复位**：系统重新初始化
- **外部复位**：系统重新初始化
- **SD卡插拔**：存储状态变化

## 状态机优势

### 清晰的系统逻辑
- **状态明确**：每个状态职责清晰
- **转换条件明确**：状态转换条件明确定义
- **易于理解**：系统行为可预测

### 良好的可维护性
- **模块化设计**：每个状态独立处理
- **易于扩展**：添加新状态和转换
- **调试友好**：状态跟踪和日志记录

### 系统可靠性
- **错误隔离**：错误状态独立处理
- **恢复机制**：多层次的错误恢复
- **状态保护**：防止非法状态转换
