#ifndef __FLASH_APP_H_
#define __FLASH_APP_H_
#include "stdint.h"


/* 原有Flash操作函数 */
uint8_t test_flash(void);
uint8_t flash_write_data(uint32_t addr, uint8_t *data, uint16_t len);
uint8_t flash_read_data(uint32_t addr, uint8_t *data, uint16_t len);
uint8_t flash_read(uint32_t addr, uint8_t *data, uint16_t len);
void flash_stored(void);
void flash_threshold_stored(float threshold_value);
float flash_threshold_read(void);
void flash_ratio_stored(float ratio_value);
float flash_ratio_read(void);
uint8_t flash_read_direct(uint32_t addr, uint16_t len);
void flash_ratio_threshold_init(void);
void config_set_waiting_limit_input(void);
/* 仅移植实际用到的config函数 */
uint8_t config_process_input(uint8_t *input_buffer); // 处理用户输入
uint8_t config_is_waiting_input(void);               // 检查是否等待输入
void config_set_waiting_ratio_input(void);           // 设置等待变比输入标志

void power_on_count_reset(void);
	
void power_on_count_reset_permanent(void);
	
extern float input_radio;
extern float input_threshold;
// 为了兼容性，添加别名
#define input_value input_radio

/* 上电次数计数器 */
extern uint32_t power_on_count;
void power_on_count_init(void);        // 初始化上电次数
void power_on_count_increment(void);   // 上电次数+1并保存
uint32_t power_on_count_read(void);    // 读取上电次数

void flash_keynum_stored_silent(uint8_t keynum_value);
uint8_t flash_keynum_read_silent(void);

#endif /* __FLASH_APP_H_ */
