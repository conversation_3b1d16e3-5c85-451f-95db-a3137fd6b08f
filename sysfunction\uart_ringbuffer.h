#ifndef __UART_RINGBUFFER_H__
#define __UART_RINGBUFFER_H__

#include "ringbuffer.h"
#include "bsp_gd32f470vet6.h"

#ifdef __cplusplus
extern "C" {
#endif

// 环形缓冲区大小定义
#define UART_RX_BUFFER_SIZE    1024

// 声明DMA接收缓冲区（全局变量）
extern uint8_t dma_rx_buffer[512];

// 初始化UART环形缓冲区
void uart_ringbuffer_init(void);

// 从环形缓冲区读取数据
uint32_t uart_ringbuffer_read(uint8_t *buffer, uint32_t size);

// 获取环形缓冲区中可读数据长度
uint32_t uart_ringbuffer_available(void);

// UART接收中断处理函数（在中断中调用）
void uart_ringbuffer_irq_handler(void);

#ifdef __cplusplus
}
#endif

#endif /* __UART_RINGBUFFER_H__ */
