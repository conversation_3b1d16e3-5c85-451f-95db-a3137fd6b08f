#include "bsp_gd32f470vet6.h"

#define RTC_CLOCK_SOURCE_LXTAL		// 配置RTC时钟源
#define BKP_VALUE    0x32F0

rtc_parameter_struct   rtc_initpara;
rtc_alarm_struct  rtc_alarm;
__IO uint32_t prescaler_a = 0, prescaler_s = 0;
uint32_t RTCSRC_FLAG = 0;

uint8_t oled_flag;

// 等待日期时间输入标志
static uint8_t waiting_for_datetime_input = 0;

/**
 * @brief  检查是否正在等待日期时间输入
 * @param  none
 * @retval uint8_t: 1表示正在等待，0表示不在等待
 */
uint8_t rtc_is_waiting_datetime_input(void)
{
    return waiting_for_datetime_input;
}

/**
 * @brief  处理日期时间输入命令
 * @param  none
 * @retval none
 */
void rtc_handle_datetime_command(void)
{
    rtc_show_time();
    my_printf(DEBUG_USART, "请输入日期时间 (格式: 2025年01月01日12:00:30):\r\n");
    waiting_for_datetime_input = 1; // 设置等待输入标志
}

/**
 * @brief  处理用户输入的日期时间
 * @param  input_buffer: 用户输入的字符串
 * @retval uint8_t: 1表示处理成功，0表示处理失败
 */
uint8_t rtc_process_datetime_input(uint8_t *input_buffer)
{
    if (!waiting_for_datetime_input)
    {
        return 0; // 不是在等待日期时间输入
    }
    
    waiting_for_datetime_input = 0; // 清除等待标志
    
    // 设置RTC时间
    RTC_StatusTypeDef status = rtc_set_time_from_string_ex((const char *)input_buffer);
    
    if (status == RTC_STATUS_OK)
    {
        // 显示设置的时间
        rtc_show_set_time();
        my_printf(DEBUG_USART, "RTC时间设置成功\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "RTC时间设置失败\r\n");
    }
    
    return 1;
}





/*!
    \brief      display the current time
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_task(void)
{
    rtc_current_time_get(&rtc_initpara);
		
	if(oled_flag ){
    oled_printf(0, 0, "%0.2x:%0.2x:%0.2x", rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
	}
	if(oled_flag == 0){ oled_printf(0, 0, "system idle"); 
	}
}






/**
 * @brief  解析时间字符串并设置到GD32标准库的rtc_parameter_struct结构体
 * @param  time_str: 包含时间信息的字符串
 * @param  rtc_param: 指向rtc_parameter_struct结构体的指针
 * @retval RTC_StatusTypeDef: 操作状态
 */
static RTC_StatusTypeDef parse_time_string_gd32(const char *time_str, rtc_parameter_struct *rtc_param)
{
    if (time_str == NULL || rtc_param == NULL)
    {
        return RTC_STATUS_ERROR;
    }

    int year, month, day, hour, minute, second;
    int parsed = 0;

    // 尝试解析中文格式: "2025年01月01日12:00:30"
    parsed = sscanf(time_str, "%d年%d月%d日%d:%d:%d", &year, &month, &day, &hour, &minute, &second);

    // 如果中文格式解析失败，尝试标准格式: "2025-01-01 12:00:30"
    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 如果标准格式解析失败，尝试紧凑格式: "2025-01-01 01-30-10"
    if (parsed != 6)
    {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", &year, &month, &day, &hour, &minute, &second);
    }

    // 如果所有格式都解析失败
    if (parsed != 6)
    {
        return RTC_STATUS_ERROR; // 解析失败
    }

    // 参数验证
    if (year < 2000 || year > 2099 || month < 1 || month > 12 ||
        day < 1 || day > 31 || hour > 23 || minute > 59 || second > 59)
    {
        return RTC_STATUS_ERROR;
    }

    // 计算星期几 (基于Zeller公式)
    int zeller_month = month;
    int zeller_year = year;
    if (zeller_month == 1 || zeller_month == 2) {
        zeller_month += 12;
        zeller_year--;
    }
    int week = (day + 2*zeller_month + 3*(zeller_month+1)/5 + zeller_year + zeller_year/4 - zeller_year/100 + zeller_year/400) % 7;
    uint8_t day_of_week = week + 1; // 转换为1-7表示周一到周日

    // 设置GD32 RTC参数结构体
    // 将十进制值转换为BCD格式
    rtc_param->year = ((year - 2000) / 10) << 4 | ((year - 2000) % 10);
    rtc_param->month = (month / 10) << 4 | (month % 10);
    rtc_param->date = (day / 10) << 4 | (day % 10);
    rtc_param->hour = (hour / 10) << 4 | (hour % 10);
    rtc_param->minute = (minute / 10) << 4 | (minute % 10);
    rtc_param->second = (second / 10) << 4 | (second % 10);
    rtc_param->day_of_week = day_of_week;
    rtc_param->display_format = RTC_24HOUR;
    rtc_param->am_pm = RTC_AM;

    return RTC_STATUS_OK;
}

/**
 * @brief  从字符串设置RTC时间，使用完整的RTC_Init流程
 * @param  time_str: 包含时间信息的字符串，格式如"2025年01月01日12:00:30"
 * @retval RTC_StatusTypeDef: 操作状态
 */
RTC_StatusTypeDef rtc_set_time_from_string_ex(const char *time_str)
{
    if (time_str == NULL)
    {
        my_printf(DEBUG_USART, "时间字符串为空\r\n");
        return RTC_STATUS_ERROR;
    }
    
    // 创建本地变量，避免修改全局变量
    rtc_parameter_struct local_rtc_param;
    
    // 设置默认值
    local_rtc_param.factor_asyn = prescaler_a;
    local_rtc_param.factor_syn = prescaler_s;
    local_rtc_param.display_format = RTC_24HOUR;
    local_rtc_param.am_pm = RTC_AM;
    
    // 解析时间字符串并填充local_rtc_param结构体
    if (parse_time_string_gd32(time_str, &local_rtc_param) != RTC_STATUS_OK)
    {
        my_printf(DEBUG_USART, "解析时间字符串失败\r\n");
        return RTC_STATUS_ERROR;
    }
    
    // 显示解析结果
//    my_printf(DEBUG_USART, "解析结果: 20%02x年%02x月%02x日(星期%d) %02x:%02x:%02x\r\n", 
//              local_rtc_param.year, local_rtc_param.month, local_rtc_param.date, 
//              local_rtc_param.day_of_week, local_rtc_param.hour, local_rtc_param.minute, local_rtc_param.second);
    
    // 完整的RTC初始化流程
    
    // 1. 启用PMU时钟
    rcu_periph_clock_enable(RCU_PMU);
    
    // 2. 启用RTC寄存器访问
    pmu_backup_write_enable();
    
    // 3. 重新配置RTC时钟源和预分频器
    rtc_pre_config();
    
    // 确保预分频器值正确设置
    local_rtc_param.factor_asyn = prescaler_a;
    local_rtc_param.factor_syn = prescaler_s;
    
    // 4. 禁用写保护
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    // 5. 进入初始化模式 - 这会暂停RTC计数
    if (rtc_init_mode_enter() == ERROR)
    {
        my_printf(DEBUG_USART, "进入RTC初始化模式失败\r\n");
        RTC_WPK = RTC_LOCK_KEY; // 重新启用写保护
        return RTC_STATUS_ERROR;
    }
    
    // 6. 设置RTC寄存器
    // 设置预分频器
    RTC_PSC = (uint32_t)(PSC_FACTOR_A(local_rtc_param.factor_asyn) | 
                         PSC_FACTOR_S(local_rtc_param.factor_syn));
    
    // 设置时间
    uint32_t time_reg = (local_rtc_param.am_pm | 
                        TIME_HR(local_rtc_param.hour) | 
                        TIME_MN(local_rtc_param.minute) | 
                        TIME_SC(local_rtc_param.second));
    
    // 设置日期
    uint32_t date_reg = (DATE_YR(local_rtc_param.year) | 
                         DATE_DOW(local_rtc_param.day_of_week) | 
                         DATE_MON(local_rtc_param.month) | 
                         DATE_DAY(local_rtc_param.date));
    
    // 写入时间和日期寄存器
    RTC_TIME = time_reg;
    RTC_DATE = date_reg;
    
    // 设置24小时格式
    RTC_CTL &= (uint32_t)(~RTC_CTL_CS);
    RTC_CTL |= local_rtc_param.display_format;
    
    // 7. 退出初始化模式 - 这会恢复RTC计数
    rtc_init_mode_exit();
    
    // 8. 等待寄存器同步 - 确保所有设置都已应用
    rtc_register_sync_wait();
    
    // 9. 启用写保护
    RTC_WPK = RTC_LOCK_KEY;
    
    // 10. 设置备份寄存器值，表示RTC已配置
    RTC_BKP0 = BKP_VALUE;
    
    // 11. 更新全局变量，保持一致性
    rtc_initpara = local_rtc_param;
    
    // 不再进行验证，因为RTC可能已经开始计数，导致读取的时间与设置的时间不一致
    
    return RTC_STATUS_OK;
}

/**
 * @brief  调试RTC初始化失败问题
 * @param  time_str: 包含时间信息的字符串
 * @retval RTC_StatusTypeDef: 操作状态
 */
RTC_StatusTypeDef rtc_set_time_from_string_debug(const char *time_str)
{
    if (time_str == NULL)
    {
        my_printf(DEBUG_USART, "时间字符串为空\r\n");
        return RTC_STATUS_ERROR;
    }
    
    rtc_parameter_struct rtc_param;
    
    // 设置默认值
    rtc_param.factor_asyn = prescaler_a;
    rtc_param.factor_syn = prescaler_s;
    
    // 打印当前预分频器值
    my_printf(DEBUG_USART, "预分频器值: A=%d, S=%d\r\n", prescaler_a, prescaler_s);
    
    // 解析时间字符串并填充rtc_param结构体
    if (parse_time_string_gd32(time_str, &rtc_param) != RTC_STATUS_OK)
    {
        my_printf(DEBUG_USART, "解析时间字符串失败\r\n");
        return RTC_STATUS_ERROR;
    }
    
    // 打印解析后的BCD值
    my_printf(DEBUG_USART, "解析结果(BCD): 年=0x%02x, 月=0x%02x, 日=0x%02x, 星期=%d\r\n", 
              rtc_param.year, rtc_param.month, rtc_param.date, rtc_param.day_of_week);
    my_printf(DEBUG_USART, "解析结果(BCD): 时=0x%02x, 分=0x%02x, 秒=0x%02x\r\n", 
              rtc_param.hour, rtc_param.minute, rtc_param.second);
    
    // 尝试进入初始化模式
    my_printf(DEBUG_USART, "尝试进入初始化模式...\r\n");
    
    // 禁用写保护
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    ErrStatus init_status = rtc_init_mode_enter();
    if (init_status == ERROR)
    {
        my_printf(DEBUG_USART, "进入初始化模式失败\r\n");
        // 启用写保护
        RTC_WPK = RTC_LOCK_KEY;
        return RTC_STATUS_ERROR;
    }
    
    my_printf(DEBUG_USART, "进入初始化模式成功，尝试初始化RTC...\r\n");
    
    // 使用GD32标准库函数设置RTC时间
    ErrStatus rtc_status = rtc_init(&rtc_param);
    
    if (rtc_status == ERROR)
    {
        my_printf(DEBUG_USART, "rtc_init失败\r\n");
        // 退出初始化模式
        rtc_init_mode_exit();
        // 启用写保护
        RTC_WPK = RTC_LOCK_KEY;
        return RTC_STATUS_ERROR;
    }
    
    my_printf(DEBUG_USART, "rtc_init成功\r\n");
    
    // 退出初始化模式
    rtc_init_mode_exit();
    
    // 等待寄存器同步
    ErrStatus sync_status = rtc_register_sync_wait();
    if (sync_status == ERROR)
    {
        my_printf(DEBUG_USART, "寄存器同步等待失败\r\n");
    }
    else
    {
        my_printf(DEBUG_USART, "寄存器同步成功\r\n");
    }
    
    // 启用写保护
    RTC_WPK = RTC_LOCK_KEY;
    
    // 验证设置是否成功
    rtc_parameter_struct verify_params;
    rtc_current_time_get(&verify_params);
    
    my_printf(DEBUG_USART, "RTC设置后的值: 20%02x-%02x-%02x %02x:%02x:%02x\r\n",
              verify_params.year, verify_params.month, verify_params.date,
              verify_params.hour, verify_params.minute, verify_params.second);
    
    return RTC_STATUS_OK;
}


void rtc_pre_config(void)
{
    #if defined (RTC_CLOCK_SOURCE_IRC32K)
          rcu_osci_on(RCU_IRC32K);
          rcu_osci_stab_wait(RCU_IRC32K);
          rcu_rtc_clock_config(RCU_RTCSRC_IRC32K);

          prescaler_s = 0x13F;
          prescaler_a = 0x63;
    #elif defined (RTC_CLOCK_SOURCE_LXTAL)
          rcu_osci_on(RCU_LXTAL);
          rcu_osci_stab_wait(RCU_LXTAL);
          rcu_rtc_clock_config(RCU_RTCSRC_LXTAL);

          prescaler_s = 0xFF;
          prescaler_a = 0x7F;
    #else
    #error RTC clock source should be defined.
    #endif /* RTC_CLOCK_SOURCE_IRC32K */

    rcu_periph_clock_enable(RCU_RTC);
    rtc_register_sync_wait();
}

/**
 * @brief  显示当前RTC时间
 * @param  none
 * @retval none
 */
void rtc_show_time(void)
{
    // 使用局部变量，不影响全局rtc_initpara
    rtc_parameter_struct current_time;

    // 禁用写保护以确保读取正确
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;

    // 读取当前时间
    rtc_current_time_get(&current_time);

    // 重新启用写保护
    RTC_WPK = RTC_LOCK_KEY;

    my_printf(DEBUG_USART, " 20%02x-%02x-%02x %02x:%02x:%02x\r\n",
           current_time.year, current_time.month, current_time.date,
           current_time.hour, current_time.minute, current_time.second);
}

void rtc_adcshow_time(void)
{
    // 使用局部变量，不影响全局rtc_initpara
    rtc_parameter_struct current_time;

    // 禁用写保护以确保读取正确
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;

    // 读取当前时间
    rtc_current_time_get(&current_time);

    // 重新启用写保护
    RTC_WPK = RTC_LOCK_KEY;

    my_printf(DEBUG_USART, " 20%02x-%02x-%02x %02x:%02x:%02x",
           current_time.year, current_time.month, current_time.date,
           current_time.hour, current_time.minute, current_time.second);
}

/**
 * @brief       获取当前时间
 * @param       time: 时间结构体指针
 * @retval      无
 */
void get_current_time(rtc_parameter_struct *time)
{
    // 禁用写保护以确保读取正确
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;

    // 读取当前时间
    rtc_current_time_get(time);

    // 重新启用写保护
    RTC_WPK = RTC_LOCK_KEY;
}

/**
 * @brief  显示设置的RTC时间（不读取当前时间）
 * @param  none
 * @retval none
 */
void rtc_show_set_time(void)
{
    my_printf(DEBUG_USART, "\r\nSet time: 20%02x-%02x-%02x : %02x:%02x:%02x\r\n", 
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}



/*!
    \brief      get the input character string and check if it is valid
    \param[in]  none
    \param[out] none
    \retval     input value in BCD mode
*/
uint8_t usart_input_threshold(uint32_t value)
{
    uint32_t index = 0;
    uint32_t tmp[2] = {0, 0};

    while (index < 2){
        while (RESET == usart_flag_get(USART0, USART_FLAG_RBNE));
        tmp[index++] = usart_data_receive(USART0);
        if ((tmp[index - 1] < 0x30) || (tmp[index - 1] > 0x39)){
            my_printf(DEBUG_USART, "\n\r please input a valid number between 0 and 9 \n\r");
            index--;
        }
    }

    index = (tmp[1] - 0x30) + ((tmp[0] - 0x30) * 10);
    if (index > value){
        my_printf(DEBUG_USART, "\n\r please input a valid number between 0 and %d \n\r", value);
        return 0xFF;
    }

    index = (tmp[1] - 0x30) + ((tmp[0] - 0x30) <<4);
    return index;
}




/*!
    \brief      use hyperterminal to setup RTC time and alarm
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_setup(void)
{
    /* setup RTC time value */
		uint32_t tmp_year = 0xFF, tmp_month = 0xFF, tmp_day = 0xFF;
    uint32_t tmp_hh = 0xFF, tmp_mm = 0xFF, tmp_ss = 0xFF;

    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    rtc_initpara.year = 0x16;
    rtc_initpara.day_of_week = RTC_SATURDAY;
    rtc_initpara.month = RTC_APR;
    rtc_initpara.date = 0x30;
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;

    /* current time input */
    my_printf(DEBUG_USART, "=======Configure RTC Time========\n\r");
	 my_printf(DEBUG_USART, "  please set the last two digits of current year:\n\r");
    while(tmp_year == 0xFF) {
        tmp_year = usart_input_threshold(99);
        rtc_initpara.year = tmp_year;
    }
    my_printf(DEBUG_USART, "  20%0.2x\n\r", tmp_year);

    my_printf(DEBUG_USART, "  please input month:\n\r");
    while(tmp_month == 0xFF) {
        tmp_month = usart_input_threshold(12);
        rtc_initpara.month = tmp_month;
    }
    my_printf(DEBUG_USART, "  %0.2x\n\r", tmp_month);

    my_printf(DEBUG_USART, "  please input day:\n\r");
    while(tmp_day == 0xFF) {
        tmp_day = usart_input_threshold(31);
        rtc_initpara.date = tmp_day;
    }
    my_printf(DEBUG_USART, "  %0.2x\n\r", tmp_day);
		
	
    my_printf(DEBUG_USART, "  please input hour:\n\r");
    while (0xFF == tmp_hh){
        tmp_hh = usart_input_threshold(23);
        rtc_initpara.hour = tmp_hh;
    }
    my_printf(DEBUG_USART, "  %0.2x\n\r", tmp_hh);

    my_printf(DEBUG_USART, "  please input minute:\n\r");
    while (0xFF == tmp_mm){
        tmp_mm = usart_input_threshold(59);
        rtc_initpara.minute = tmp_mm;
    }
    my_printf(DEBUG_USART, "  %0.2x\n\r", tmp_mm);

    my_printf(DEBUG_USART, "  please input second:\n\r");
    while (0xFF == tmp_ss){
        tmp_ss = usart_input_threshold(59);
        rtc_initpara.second = tmp_ss;
    }
    my_printf(DEBUG_USART, "  %0.2x\n\r", tmp_ss);

    /* RTC current time configuration */
    if(ERROR == rtc_init(&rtc_initpara)){
        my_printf(DEBUG_USART, "\n\r** RTC time configuration failed! **\n\r");
    }else{
        my_printf(DEBUG_USART, "\n\r** RTC time configuration success! **\n\r");
        rtc_show_time();
        RTC_BKP0 = BKP_VALUE;
    }

//    /* setup RTC alarm */
//    tmp_hh = 0xFF;
//    tmp_mm = 0xFF;
//    tmp_ss = 0xFF;

//    rtc_alarm_disable(RTC_ALARM0);
//    printf("=======Input Alarm Value=======\n\r");
//    rtc_alarm.alarm_mask = RTC_ALARM_DATE_MASK|RTC_ALARM_HOUR_MASK|RTC_ALARM_MINUTE_MASK;
//    rtc_alarm.weekday_or_date = RTC_ALARM_DATE_SELECTED;
//    rtc_alarm.alarm_day = 0x31;
//    rtc_alarm.am_pm = RTC_AM;

//    /* RTC alarm input */
//    printf("  please input Alarm Hour:\n\r");
//    while (0xFF == tmp_hh){
//        tmp_hh = usart_input_threshold(23);
//        rtc_alarm.alarm_hour = tmp_hh;
//    }
//    printf("  %0.2x\n\r", tmp_hh);

//    printf("  Please Input Alarm Minute:\n\r");
//    while (0xFF == tmp_mm){
//        tmp_mm = usart_input_threshold(59);
//        rtc_alarm.alarm_minute = tmp_mm;
//    }
//    printf("  %0.2x\n\r", tmp_mm);

//    printf("  Please Input Alarm Second:\n\r");
//    while (0xFF == tmp_ss){
//        tmp_ss = usart_input_threshold(59);
//        rtc_alarm.alarm_second = tmp_ss;
//    }
//    printf("  %0.2x", tmp_ss);

//    /* RTC alarm configuration */
//    rtc_alarm_config(RTC_ALARM0,&rtc_alarm);
//    printf("\n\r** RTC Set Alarm Success!  **\n\r");
//    rtc_show_alarm();

//    rtc_interrupt_enable(RTC_INT_ALARM0);
//    rtc_alarm_enable(RTC_ALARM0);
}

/**
 * @brief  修改rtc_setup函数，使用全局变量rtc_initpara中的时间参数
 * @param  none
 * @retval none
 */
void rtc_setup_with_params(void)
{
    /* 使用全局变量rtc_initpara中的参数设置RTC时间 */
    
    /* 禁用写保护 */
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    /* 确保预分频器值正确设置 */
    rtc_initpara.factor_asyn = prescaler_a;
    rtc_initpara.factor_syn = prescaler_s;
    
    /* 确保显示格式和AM/PM设置正确 */
    rtc_initpara.display_format = RTC_24HOUR;
    rtc_initpara.am_pm = RTC_AM;
    
    /* 打印调试信息 */
    my_printf(DEBUG_USART, "设置RTC参数: 20%02x年%02x月%02x日(星期%d) %02x:%02x:%02x\r\n", 
              rtc_initpara.year, rtc_initpara.month, rtc_initpara.date, 
              rtc_initpara.day_of_week, rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
    my_printf(DEBUG_USART, "预分频器值: A=0x%x, S=0x%x\r\n", rtc_initpara.factor_asyn, rtc_initpara.factor_syn);
    
    /* 进入初始化模式 */
    ErrStatus init_status = rtc_init_mode_enter();
    if(ERROR == init_status){
        my_printf(DEBUG_USART, "\n\r** 进入RTC初始化模式失败! **\n\r");
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return;
    }
    
    /* 设置RTC时间 */
    ErrStatus rtc_status = rtc_init(&rtc_initpara);
    
    if(ERROR == rtc_status){
        my_printf(DEBUG_USART, "\n\r** RTC时间配置失败! **\n\r");
    }else{
        my_printf(DEBUG_USART, "\n\r** RTC时间配置成功! **\n\r");
        rtc_show_time();
        RTC_BKP0 = BKP_VALUE;
    }
    
    /* 退出初始化模式 */
    rtc_init_mode_exit();
    
    /* 等待寄存器同步 */
    rtc_register_sync_wait();
    
    /* 启用写保护 */
    RTC_WPK = RTC_LOCK_KEY;
}

/**
 * @brief  直接设置RTC时间，不使用rtc_init函数
 * @param  none
 * @retval none
 */
void rtc_setup_direct(void)
{
    /* 禁用写保护 */
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;
    
    /* 进入初始化模式 */
    if(ERROR == rtc_init_mode_enter()){
        my_printf(DEBUG_USART, "\n\r** 进入RTC初始化模式失败! **\n\r");
        /* 启用写保护 */
        RTC_WPK = RTC_LOCK_KEY;
        return;
    }
    
    /* 直接设置RTC寄存器，而不是使用rtc_init函数 */
    /* 设置预分频器 */
    RTC_PSC = (uint32_t)(PSC_FACTOR_A(rtc_initpara.factor_asyn) | 
                         PSC_FACTOR_S(rtc_initpara.factor_syn));
    
    /* 设置时间 */
    uint32_t time_reg = (rtc_initpara.am_pm | 
                        TIME_HR(rtc_initpara.hour) | 
                        TIME_MN(rtc_initpara.minute) | 
                        TIME_SC(rtc_initpara.second));
    
    /* 设置日期 */
    uint32_t date_reg = (DATE_YR(rtc_initpara.year) | 
                         DATE_DOW(rtc_initpara.day_of_week) | 
                         DATE_MON(rtc_initpara.month) | 
                         DATE_DAY(rtc_initpara.date));
    
    /* 写入时间和日期寄存器 */
    RTC_TIME = time_reg;
    RTC_DATE = date_reg;
    
    /* 设置24小时格式 */
    RTC_CTL &= (uint32_t)(~RTC_CTL_CS);
    RTC_CTL |= rtc_initpara.display_format;
    
    /* 退出初始化模式 */
    rtc_init_mode_exit();
    
    /* 等待寄存器同步 */
    ErrStatus sync_status = rtc_register_sync_wait();
    if(ERROR == sync_status){
        my_printf(DEBUG_USART, "\n\r** 寄存器同步等待失败! **\n\r");
    }
    
    /* 启用写保护 */
    RTC_WPK = RTC_LOCK_KEY;
    
    /* 显示设置后的时间 */
    rtc_show_time();
    RTC_BKP0 = BKP_VALUE;
}


void RTC_Init(void)
{
    my_printf(DEBUG_USART, "\n\r  ****************** RTC calendar demo ******************\n\r");

    /* enable PMU clock */
    rcu_periph_clock_enable(RCU_PMU);
    /* enable the access of the RTC registers */
    pmu_backup_write_enable();
    
    rtc_pre_config();
    /* get RTC clock entry selection */
    RTCSRC_FLAG = GET_BITS(RCU_BDCTL, 8, 9);

    /* 检查RTC是否已配置，或时钟源是否已设置 */
    if((BKP_VALUE != RTC_BKP0) || (0x00 == RTCSRC_FLAG)){
        /* backup data register value is not correct or not yet programmed
        or RTC clock source is not configured (when the first time the program 
        is executed or data in RCU_BDCTL is lost due to Vbat feeding) */
        my_printf(DEBUG_USART, "RTC未配置或时钟源未设置，执行初始化...\n\r");
        rtc_setup();
    }else{
        /* detect the reset source */
        if (RESET != rcu_flag_get(RCU_FLAG_PORRST)){
            my_printf(DEBUG_USART, "检测到电源复位...\n\r");
            
            // 电源复位后，确保RTC配置正确
            // 重新配置RTC时钟源和预分频器，但不修改时间
            rtc_pre_config();
        }else if (RESET != rcu_flag_get(RCU_FLAG_EPRST)){
            my_printf(DEBUG_USART, "检测到外部复位...\n\r");
        }
        my_printf(DEBUG_USART, "RTC已配置，无需重新初始化...\n\r");

        rtc_show_time();
    }
    rcu_all_reset_flag_clear();
}



/**
 * @brief  将当前RTC时间转换为Unix时间戳（4字节HEX格式）
 * @param  none
 * @retval uint32_t: Unix时间戳（大端序HEX格式）
 * @note   示例：2025-01-01 12:30:45 → 1735705845 → 0x6774C4F5
 */
uint32_t rtc_to_unix_timestamp_hex(void)
{
    rtc_parameter_struct current_time;

    // 禁用写保护以确保读取正确
    RTC_WPK = RTC_UNLOCK_KEY1;
    RTC_WPK = RTC_UNLOCK_KEY2;

    // 读取当前时间
    rtc_current_time_get(&current_time);

    // 重新启用写保护
    RTC_WPK = RTC_LOCK_KEY;

    // 将BCD格式转换为十进制
    uint16_t year = 2000 + ((current_time.year >> 4) * 10) + (current_time.year & 0x0F);
    uint8_t month = ((current_time.month >> 4) * 10) + (current_time.month & 0x0F);
    uint8_t day = ((current_time.date >> 4) * 10) + (current_time.date & 0x0F);
    uint8_t hour = ((current_time.hour >> 4) * 10) + (current_time.hour & 0x0F);
    uint8_t minute = ((current_time.minute >> 4) * 10) + (current_time.minute & 0x0F);
    uint8_t second = ((current_time.second >> 4) * 10) + (current_time.second & 0x0F);

    // 计算从1970年1月1日到指定日期的天数
    uint32_t days = 0;

    // 计算年份贡献的天数
    for (uint16_t y = 1970; y < year; y++) {
        if ((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days += 366; // 闰年
        } else {
            days += 365; // 平年
        }
    }

    // 每月天数表（平年）
    const uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 计算月份贡献的天数
    for (uint8_t m = 1; m < month; m++) {
        days += days_in_month[m - 1];
        // 如果是闰年且已经过了2月，需要加1天
        if (m == 2 && ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0))) {
            days += 1;
        }
    }

    // 加上当月的天数（减1因为当天还没过完）
    days += (day - 1);

    // 计算Unix时间戳
    uint32_t unix_timestamp = days * 86400 + hour * 3600 + minute * 60 + second;

    return unix_timestamp;
}


