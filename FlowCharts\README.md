# DSB系统流程图集合

本文件夹包含了DSB数据采集与存储系统的所有流程图，使用Mermaid语法编写。

## 📁 文件列表

1. **01_系统总体架构流程图.md** - 系统整体运行逻辑
2. **02_系统初始化详细流程图.md** - main函数初始化流程
3. **03_任务调度器运行流程图.md** - scheduler_run函数逻辑
4. **04_ADC数据处理详细流程图.md** - adc_process_task函数逻辑
5. **05_数据存储系统流程图.md** - 数据存储处理流程
6. **06_串口命令处理流程图.md** - uart_task命令解析流程
7. **07_定时器中断处理流程图.md** - TIMER6中断处理流程
8. **08_系统状态机流程图.md** - 系统状态转换逻辑

## 🔧 使用方法

### 在线查看
1. 复制Mermaid代码
2. 访问 [Mermaid Live Editor](https://mermaid.live/)
3. 粘贴代码即可查看图形

### 本地查看
1. 安装支持Mermaid的Markdown编辑器（如Typora、Mark Text）
2. 直接打开.md文件查看

### 集成到文档
1. 可以直接将Mermaid代码嵌入到Markdown文档中
2. 支持GitHub、GitLab等平台的Mermaid渲染

## 📊 流程图说明

每个流程图都包含：
- **标题**：清晰的图表名称
- **节点**：系统中的关键步骤和决策点
- **连接线**：流程的执行顺序和条件
- **颜色标识**：不同类型节点的视觉区分
- **注释**：重要节点的详细说明

## 🎨 颜色说明

- **蓝色** (#e3f2fd)：起始节点
- **绿色** (#c8e6c9)：成功/正常流程
- **黄色** (#fff9c4)：判断/决策节点
- **橙色** (#fff3e0)：主要处理流程
- **粉色** (#fce4ec)：数据处理节点
- **红色** (#ffebee)：错误/异常处理

## 📝 更新说明

- 创建日期：2025年1月
- 版本：v1.0
- 对应代码版本：DSB系统当前版本
- 更新频率：随代码变更同步更新

## 🔄 维护建议

1. **代码变更时同步更新流程图**
2. **定期检查流程图与实际代码的一致性**
3. **新增功能时补充相应的流程图**
4. **保持流程图的简洁性和可读性**
