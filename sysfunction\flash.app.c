#include "bsp_gd32f470vet6.h"

// 仅保留实际用到的config相关变量
static uint8_t waiting_for_ratio_input = 0;  // 等待变比输入标志
static uint8_t waiting_for_limit_input = 0;  // 等待阈值输入标志
float input_radio;
float input_threshold;

// 上电次数计数器
uint32_t power_on_count = 0;

extern int my_printf(uint32_t usart_periph, const char *format, ...);

uint8_t test_flash()
{
	uint32_t flash_id = 0;

	 /* get flash id */
    flash_id = spi_flash_read_id();
	my_printf(DEBUG_USART, "Flash ID: 0x%lX\r\n", flash_id);

	/* flash id is correct */
    if(SFLASH_ID == flash_id)
	{
		my_printf(DEBUG_USART,"flash...........ok\n");
	}
}


//上电次数清零（仅清零内存变量，不保存到Flash）
void power_on_count_reset(void)
{
    // 仅将内存中的上电次数设为0，不保存到Flash
    // 这样可以临时清零显示，但重启后会恢复正常计数
    power_on_count = 0;

    my_printf(DEBUG_USART, "Power on count reset to 0 (memory only, will restore after reboot)\r\n");
}


//上电次数彻底清零并保存到Flash
void power_on_count_reset_permanent(void)
{
    // 使用特殊标志值0xFFFFFFFE来标识清零状态
    // 下次启动时检测到这个值会设为0且不进行+1操作
    uint32_t reset_flag = 0xFFFFFFFE; // 清零标志值

    // 将标志值转换为字节数组
    uint8_t count_data[sizeof(uint32_t)];
    memcpy(count_data, &reset_flag, sizeof(uint32_t));

    // 将标志值写入Flash
    uint8_t write_result = flash_write_data(Power_Count_ADDR, count_data, sizeof(uint32_t));

    if (write_result == 0) {
        power_on_count = 0; // 同时清零内存中的值
        my_printf(DEBUG_USART, "Power on count permanently reset, will be 0 after next reboot\r\n");
    } else {
        my_printf(DEBUG_USART, "Failed to permanently reset power on count to Flash, error: %d\r\n", write_result);
    }
}

/**
 * @brief 向Flash写入数据（扇区擦除后写入）
 * @param addr 写入地址（必须是4KB对齐的）
 * @param data 要写入的数据
 * @param len 数据长度（字节）
 * @return 0:成功, 非0:失败
 * @note 此函数会先擦除整个扇区再写入数据
 */
uint8_t flash_write_data(uint32_t addr, uint8_t *data, uint16_t len)
{
    uint32_t sector_addr;
    uint8_t result = 0;
    uint8_t retry_count = 0;
    const uint8_t MAX_RETRY = 3;

    // 参数有效性检查
    if (data == NULL || len == 0 || len > SPI_FLASH_SECTOR_SIZE) {
        return 1; // 参数错误
    }

    // 计算扇区起始地址(4KB对齐)
    sector_addr = addr & ~(SPI_FLASH_SECTOR_SIZE - 1);

    // 重试机制：最多重试3次
    do {
        retry_count++;

        // 擦除扇区
        spi_flash_sector_erase(sector_addr);

        // 等待擦除完成
        spi_flash_wait_for_write_end();

        // 写入数据
        spi_flash_buffer_write(data, addr, len);

        // 等待写入完成
        spi_flash_wait_for_write_end();

        // 读取数据进行验证
        uint8_t verify_buffer[SPI_FLASH_PAGE_SIZE];
        spi_flash_buffer_read(verify_buffer, addr, len);

        // 验证写入的数据是否正确
        if (flash_memory_compare(data, verify_buffer, len) == SUCCESS) {
            result = 0; // 成功
            break;
        } else {
            result = 2; // 验证失败
            if (retry_count < MAX_RETRY) {
                delay_1ms(10); // 重试前延时
            }
        }
    } while (retry_count < MAX_RETRY);

    // 如果重试失败，记录错误
    if (result != 0) {
        my_printf(DEBUG_USART, "Flash write failed after %d retries, addr: 0x%08lX\r\n",
                  MAX_RETRY, addr);
    }

    return result;
}

//初始化变比和阈值参数
void flash_ratio_threshold_init(void)
{
    // 初始化变比和阈值参数为默认值1.0并写入Flash
//    float init_ratio;
//    float init_threshold;

    input_radio = 1.0f;           // 默认变比值
    input_threshold = 1.0f;       // 默认阈值

	
    flash_ratio_stored(input_radio);
    flash_threshold_stored(input_threshold);

}


/**
 * @brief 从Flash读取数据（不进行打印输出）
 * @param addr 读取地址
 * @param data 用于存储读取数据的缓冲区
 * @param len 要读取的数据长度（字节）
 * @return 0:成功, 非0:失败
 */
uint8_t flash_read_data(uint32_t addr, uint8_t *data, uint16_t len)
{
    // 参数有效性检查
    if (data == NULL || len == 0) {
        return 1; // 参数错误
    }

    // 读取数据
    spi_flash_buffer_read(data, addr, len);

    return 0; // 成功
}

/**
 * @brief 从Flash读取数据并打印输出
 * @param addr 读取地址
 * @param data 用于存储读取数据的缓冲区
 * @param len 要读取的数据长度（字节）
 * @return 0:成功, 非0:失败
 */
uint8_t flash_read(uint32_t addr, uint8_t *data, uint16_t len)
{
    // 调用基础读取函数
    uint8_t read_result = flash_read_data(addr, data, len);

    // 打印结果
    if (read_result == 0) {
        my_printf(DEBUG_USART, "String read from Flash: %s\r\n", (char*)data);
    } else {
        my_printf(DEBUG_USART, "Failed to read string from Flash, error: %d\r\n", read_result);
    }

    return read_result;
}

/**
 * @brief 从Flash读取数据并直接打印（使用内部缓冲区）
 * @param addr 读取地址
 * @param len 要读取的数据长度（字节）
 * @return 0:成功, 非0:失败
 */
uint8_t flash_read_direct(uint32_t addr, uint16_t len)
{
    // 使用内部缓冲区
    uint8_t internal_buffer[256]; // 内部缓冲区，避免外部依赖
    uint8_t read_result;

    // 检查长度是否超出缓冲区大小
    if (len > sizeof(internal_buffer) - 1) { // 预留一个字节给字符串结束符
        my_printf(DEBUG_USART, "Error: Requested length exceeds buffer size\r\n");
        return 3; // 长度错误
    }

    // 清空缓冲区
    memset(internal_buffer, 0, sizeof(internal_buffer));

    // 读取数据
    read_result = flash_read_data(addr, internal_buffer, len);

    // 确保字符串正确结束
    internal_buffer[len] = '\0';

    // 打印结果
    if (read_result == 0) {
        my_printf(DEBUG_USART, "%s", (char*)internal_buffer);
    } else {
        my_printf(DEBUG_USART, "Failed to read string from Flash, error: %d\r\n", read_result);
    }

    return read_result;
}

//flash存储设备ID
void flash_stored(void)
{
    // 要存储的设备ID字符串
    const char *Device_ID = "2025815992";
    uint16_t Device_ID_len = strlen(Device_ID) + 1; // +1 for null terminator

    // 将字符串写入Flash
    uint8_t write_result = flash_write_data(Device_ID_ADDR, (uint8_t*)Device_ID, Device_ID_len);

    if (write_result == 0) {
     //   my_printf(DEBUG_USART, "String written to Flash successfully!\r\n");
    } else {
        my_printf(DEBUG_USART, "Failed to write string to Flash, error: %d\r\n", write_result);
        return;
    }

}

//flash存储阈值
void flash_threshold_stored(float threshold_value)
{
    // 将浮点数转换为字节数组
    uint8_t threshold_data[sizeof(float)];
    memcpy(threshold_data, &threshold_value, sizeof(float));

    // 将阈值写入Flash
    uint8_t write_result = flash_write_data(Threshold_ADDR, threshold_data, sizeof(float));

//    if (write_result == 0) {
//        my_printf(DEBUG_USART, "Threshold value %.2f written to Flash successfully!\r\n", threshold_value);
//    } else {
//        my_printf(DEBUG_USART, "Failed to write threshold value to Flash, error: %d\r\n", write_result);
//        return;
//    }
}

//flash读取阈值
float flash_threshold_read(void)
{
    uint8_t threshold_data[sizeof(float)];
    float threshold_value = 0.0f;

    // 从Flash读取阈值数据
    uint8_t read_result = flash_read_data(Threshold_ADDR, threshold_data, sizeof(float));

    if (read_result == 0) {
        // 将字节数组转换为浮点数
        memcpy(&threshold_value, threshold_data, sizeof(float));
//        my_printf(DEBUG_USART, "Threshold value %.2f read from Flash successfully!\r\n", threshold_value);
    } else {
        my_printf(DEBUG_USART, "Failed to read threshold value from Flash, error: %d\r\n", read_result);
        threshold_value = -1.0f; // 错误标志
    }

    return threshold_value;
}

//flash存储变比
void flash_ratio_stored(float ratio_value)
{
    // 将浮点数转换为字节数组
    uint8_t ratio_data[sizeof(float)];
    memcpy(ratio_data, &ratio_value, sizeof(float));

    // 将变比写入Flash
    uint8_t write_result = flash_write_data(Ratio_ADDR, ratio_data, sizeof(float));

//    if (write_result == 0) {
//        my_printf(DEBUG_USART, "Ratio value %.2f written to Flash successfully!\r\n", ratio_value);
//    } else {
//        my_printf(DEBUG_USART, "Failed to write ratio value to Flash, error: %d\r\n", write_result);
//        return;
//    }
}

//flash读取变比
float flash_ratio_read(void)
{
    uint8_t ratio_data[sizeof(float)];
    float ratio_value = 0.0f;

    // 从Flash读取变比数据
    uint8_t read_result = flash_read_data(Ratio_ADDR, ratio_data, sizeof(float));

    if (read_result == 0) {
        // 将字节数组转换为浮点数
        memcpy(&ratio_value, ratio_data, sizeof(float));
//        my_printf(DEBUG_USART, "Ratio value %.2f read from Flash successfully!\r\n", ratio_value);
    } else {
        my_printf(DEBUG_USART, "Failed to read ratio value from Flash, error: %d\r\n", read_result);
        ratio_value = -1.0f; // 错误标志
    }

    return ratio_value;
}

//flash存储KeyNum
void flash_keynum_stored_silent(uint8_t keynum_value)
{
    // 将KeyNum值转换为字节数组
    uint8_t keynum_data[sizeof(uint8_t)];
    memcpy(keynum_data, &keynum_value, sizeof(uint8_t));

    // 将KeyNum写入Flash
    uint8_t write_result = flash_write_data(KeyNum_ADDR, keynum_data, sizeof(uint8_t));

    // 无任何串口输出
}

//flash读取KeyNum（无串口输出）
uint8_t flash_keynum_read_silent(void)
{
    uint8_t keynum_data[sizeof(uint8_t)];
    uint8_t keynum_value = 1; // 默认值为1

    // 从Flash读取KeyNum数据
    uint8_t read_result = flash_read_data(KeyNum_ADDR, keynum_data, sizeof(uint8_t));

    if (read_result == 0) {
        // 将字节数组转换为uint8_t
        memcpy(&keynum_value, keynum_data, sizeof(uint8_t));

        // 数据有效性检查
        if (keynum_value < 1 || keynum_value > 10) {
            keynum_value = 1; // 使用默认值
        }
    } else {
        keynum_value = 1; // 错误时使用默认值
    }

    return keynum_value;
}



// =============== 仅移植实际用到的config功能 ===============

// 处理用户输入的数值
uint8_t config_process_input(uint8_t *input_buffer)
{
	
    // 清理输入字符串，移除换行符和回车符
    char clean_buffer[32];
    uint8_t i = 0, j = 0;
	
	
    // 复制并清理输入数据
	
	
    while (input_buffer[i] != '\0' && i < sizeof(clean_buffer) - 1)
    {
        if (input_buffer[i] != '\r' && input_buffer[i] != '\n' && input_buffer[i] != ' ')
        {
            clean_buffer[j++] = input_buffer[i];
        }
        i++;
    }
    clean_buffer[j] = '\0';

	
    if (waiting_for_ratio_input)
    {
		float temp_value = atof(clean_buffer);
	
		if((0<temp_value)&&(temp_value<100))
		{
			input_radio = atof(clean_buffer);

			// 直接使用flash存储函数

			my_printf(DEBUG_USART, "ratio = %.2f\r\n", input_radio);
			radio_Num = radio_Num+1;
			// 记录ratio配置成功
			char log_msg[64];
			sprintf(log_msg, "ratio config success to %.2f", input_radio);
			store_log_entry(log_msg);

		}
		else
		{
			my_printf(DEBUG_USART, "ratio = %.2f\r\n", input_radio);
		}
		waiting_for_ratio_input = 0;
		return 1;
    }

    if (waiting_for_limit_input)
    {
		float temp_threshold = atof(clean_buffer);
		if((0<temp_threshold)&&(temp_threshold<200))
		{
			input_threshold= atof(clean_buffer);

			// 直接使用flash存储函数

			my_printf(DEBUG_USART, "limit = %.2f\r\n", input_threshold);
			limit_Num = limit_Num+1;
			// 记录limit配置成功
			char log_msg[64];
			sprintf(log_msg, "limit config success to %.2f", input_threshold);
			store_log_entry(log_msg);

		}
		else
		{
			my_printf(DEBUG_USART, "limit = %.2f\r\n", input_threshold);
		}

		waiting_for_limit_input = 0;
		return 1;
    }

    // 检查是否为RTC日期时间输入
    extern uint8_t rtc_process_datetime_input(uint8_t *input_buffer); // 声明外部函数
    if (rtc_process_datetime_input(input_buffer))
    {
        return 1;
    }

    return 0;
}

//上电次数初始化
void power_on_count_init(void)
{
    // 从Flash读取上电次数
    power_on_count = power_on_count_read();

    // 上电次数+1
    power_on_count_increment();
}

//上电次数+1并保存到Flash
void power_on_count_increment(void)
{
    power_on_count++;

    // 将上电次数转换为字节数组
    uint8_t count_data[sizeof(uint32_t)];
    memcpy(count_data, &power_on_count, sizeof(uint32_t));

    // 将上电次数写入Flash
    uint8_t write_result = flash_write_data(Power_Count_ADDR, count_data, sizeof(uint32_t));

    if (write_result == 0) {
      //  my_printf(DEBUG_USART, "Power on count %lu saved to Flash\r\n", power_on_count);
    } else {
        my_printf(DEBUG_USART, "Failed to save power on count to Flash, error: %d\r\n", write_result);
    }
}

//从Flash读取上电次数
uint32_t power_on_count_read(void)
{
    uint8_t count_data[sizeof(uint32_t)];
    uint32_t count_value = 0;

    // 从Flash读取上电次数数据
    uint8_t read_result = flash_read_data(Power_Count_ADDR, count_data, sizeof(uint32_t));

    if (read_result == 0) {
        // 将字节数组转换为uint32_t
        memcpy(&count_value, count_data, sizeof(uint32_t));

        // 检查读取的数据是否有效（防止Flash未初始化的情况）
        if (count_value == 0xFFFFFFFF) {
            count_value = 0; // Flash未初始化，设为0
        }
    } else {
        my_printf(DEBUG_USART, "Failed to read power on count from Flash, error: %d\r\n", read_result);
        count_value = 0; // 读取失败，设为0
    }

    return count_value;
}

// 检查是否正在等待输入
uint8_t config_is_waiting_input(void)
{
    extern uint8_t rtc_is_waiting_datetime_input(void); // 声明外部函数
    return (waiting_for_ratio_input || waiting_for_limit_input || rtc_is_waiting_datetime_input());
}

// 设置等待变比输入标志
void config_set_waiting_ratio_input(void)
{
    waiting_for_ratio_input = 1;
	
}

void config_set_waiting_limit_input(void)
{
   waiting_for_limit_input = 1;
}