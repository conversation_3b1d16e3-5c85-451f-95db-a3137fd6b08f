#include "performance_monitor.h"
#include "bsp_gd32f470vet6.h"

#define MAX_TASKS 8

// 性能监控数据
static task_performance_t task_perf[MAX_TASKS];
static system_performance_t sys_perf;
static uint32_t task_start_time[MAX_TASKS];

// 初始化性能监控
void performance_monitor_init(void)
{
    // 清零所有性能数据
    memset(task_perf, 0, sizeof(task_perf));
    memset(&sys_perf, 0, sizeof(sys_perf));
    memset(task_start_time, 0, sizeof(task_start_time));
}

// 任务开始执行
void performance_task_start(uint8_t task_id)
{
    if (task_id < MAX_TASKS) {
        task_start_time[task_id] = get_system_us(); // 获取微秒级时间戳
    }
}

// 任务执行结束
void performance_task_end(uint8_t task_id)
{
    if (task_id < MAX_TASKS) {
        uint32_t end_time = get_system_us();
        uint32_t execution_time = end_time - task_start_time[task_id];
        
        task_performance_t *perf = &task_perf[task_id];
        
        // 更新性能统计
        perf->task_run_count++;
        perf->last_execution_time = execution_time;
        perf->total_execution_time += execution_time;
        
        // 更新最大执行时间
        if (execution_time > perf->max_execution_time) {
            perf->max_execution_time = execution_time;
        }
        
        // 计算平均执行时间
        perf->avg_execution_time = perf->total_execution_time / perf->task_run_count;
    }
}

// 获取系统性能统计
void performance_get_system_stats(system_performance_t *stats)
{
    if (stats) {
        *stats = sys_perf;
    }
}

// 打印性能报告
void performance_print_report(void)
{
    my_printf(DEBUG_USART, "\r\n=== Performance Report ===\r\n");
    
    for (uint8_t i = 0; i < MAX_TASKS; i++) {
        if (task_perf[i].task_run_count > 0) {
            my_printf(DEBUG_USART, "Task %d: Count=%lu, Avg=%luus, Max=%luus\r\n",
                     i, task_perf[i].task_run_count,
                     task_perf[i].avg_execution_time,
                     task_perf[i].max_execution_time);
        }
    }
    
    my_printf(DEBUG_USART, "CPU Usage: %lu%%\r\n", sys_perf.cpu_usage_percent);
    my_printf(DEBUG_USART, "========================\r\n");
}

// 微秒级时间戳获取函数（需要根据实际硬件实现）
uint32_t get_system_us(void)
{
    // 这里需要根据实际的定时器配置来实现微秒级时间戳
    // 暂时使用毫秒时间戳 * 1000 作为近似值
    return get_system_ms() * 1000;
}
