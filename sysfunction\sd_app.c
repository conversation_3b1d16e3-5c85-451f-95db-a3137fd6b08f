#include "bsp_gd32f470vet6.h"

// 全局变量，用于标记SD卡初始化状态
uint8_t sd_init_success = 0;

// 加密存储模式标志 (0=正常模式, 1=加密模式)
uint8_t encrypt_mode_enabled = 0;

FATFS fs;
FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;

sd_card_info_struct sd_cardinfo;

BYTE buffer[128];
BYTE filebuffer[128];




// 统一文件管理系统
uint16_t global_data_count = 0;            // 全局数据计数器（非静态，供外部访问，用于显示）
static char global_timestamp[16];          // 全局时间戳字符串
static uint8_t timestamp_initialized = 0;  // 时间戳是否已初始化

// 采样数据文件管理
static FIL sample_file;                    // 采样数据文件句柄
static uint8_t sample_file_open = 0;       // 采样文件是否打开
static char current_filename[64];          // 当前采样文件名
static uint16_t sample_data_count = 0;     // 采样数据计数器（独立计数）

// 超限数据文件管理
static FIL overlimit_file;                 // 超限数据文件句柄
static uint8_t overlimit_file_open = 0;    // 超限文件是否打开
static char overlimit_filename[64];        // 超限文件名
static uint16_t overlimit_data_count = 0;  // 超限数据计数器（独立计数）

// 日志文件管理
static FIL log_file;                       // 日志文件句柄
static uint8_t log_file_open = 0;          // 日志文件是否打开
static uint8_t log_initialized = 0;        // 日志是否已初始化

// 加密数据文件管理
static FIL hide_file;                      // 加密数据文件句柄
static uint8_t hide_file_open = 0;         // 加密文件是否打开
static char hide_filename[64];             // 加密文件名
static uint16_t hide_data_count = 0;       // 加密数据计数器（独立计数）



ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void sd_fatfs_init(void)
{
    // 初始化标志为失败
    sd_init_success = 0;

    nvic_irq_enable(SDIO_IRQn, 0, 0);					// 使能SDIO中断，优先级为0
	uint16_t k = 5;
    DSTATUS stat = 0;
    do
    {
        stat = disk_initialize(0); 			// 初始化SD卡设备，设备号0，如果初始化失败，重试最多5次
    }while((stat != 0) && (--k));			// 如果初始化失败，重试次数未达到上限，继续重试

     

    // 打印SD卡初始化结果
    //my_printf(DEBUG_USART, "SD Card disk_initialize: %d\r\n", stat);

    if (stat == 0) {
        // SD卡初始化成功，挂载文件系统
        FRESULT mount_result = f_mount(0, &fs);  // 挂载文件系统
     //   my_printf(DEBUG_USART, "SD Card f_mount: %d\r\n", mount_result);

        if (mount_result == FR_OK) {  // FR_OK = 0，表示成功
         //   my_printf(DEBUG_USART, "SD Card file system mounted successfully!\r\n");
            sd_init_success = 1;  // 只有在初始化和挂载都成功时才设置为1
        } else {
            my_printf(DEBUG_USART, "SD Card file system mount failed! Error: %d\r\n", mount_result);
        }
    } else {
       // my_printf(DEBUG_USART, "SD Card initialization failed! Error: %d\r\n", stat);
    }
}


/**
 * @brief       将BCD格式转换为十进制
 * @param       bcd_value: BCD格式的值
 * @retval      十进制值
 */
uint8_t bcd_to_decimal(uint8_t bcd_value)
{
    return ((bcd_value >> 4) * 10) + (bcd_value & 0x0F);
}

/**
 * @brief       获取或初始化全局时间戳
 * @param       none
 * @retval      none
 * @note        首次调用或文件关闭后生成新的时间戳
 */
static void update_global_timestamp(void)
{
    if (!timestamp_initialized) {
        rtc_parameter_struct current_time;
        get_current_time(&current_time);

        // 将BCD格式转换为十进制
        uint8_t year_dec = bcd_to_decimal(current_time.year);
        uint8_t month_dec = bcd_to_decimal(current_time.month);
        uint8_t date_dec = bcd_to_decimal(current_time.date);
        uint8_t hour_dec = bcd_to_decimal(current_time.hour);
        uint8_t minute_dec = bcd_to_decimal(current_time.minute);
        uint8_t second_dec = bcd_to_decimal(current_time.second);

        sprintf(global_timestamp, "20%02d%02d%02d%02d%02d%02d",
                year_dec, month_dec, date_dec,
                hour_dec, minute_dec, second_dec);

        timestamp_initialized = 1;
    }
}

/**
 * @brief       检查并处理采样数据计数达到10条的情况
 * @param       none
 * @retval      none
 * @note        当采样数据计数达到10时，关闭采样文件并重置状态
 */
static void check_and_reset_sample_file(void)
{
    if (sample_data_count >= 10) {
        // 关闭采样文件
        if (sample_file_open) {
            f_close(&sample_file);
            sample_file_open = 0;
        }

        // 重置采样数据计数器和时间戳状态，为下一个采样文件做准备
        sample_data_count = 0;
        timestamp_initialized = 0;
    }
}

/**
 * @brief       检查并处理超限数据计数达到10条的情况
 * @param       none
 * @retval      none
 * @note        当超限数据计数达到10时，关闭超限文件并重置状态
 */
static void check_and_reset_overlimit_file(void)
{
    if (overlimit_data_count >= 10) {
        // 关闭超限文件
        if (overlimit_file_open) {
            f_close(&overlimit_file);
            overlimit_file_open = 0;
        }

        // 重置超限数据计数器和时间戳状态，为下一个超限文件做准备
        overlimit_data_count = 0;
        timestamp_initialized = 0;
    }
}

/**
 * @brief       检查并处理加密数据计数达到10条的情况
 * @param       none
 * @retval      none
 * @note        当加密数据计数达到10时，关闭加密文件并重置状态
 */
static void check_and_reset_hide_file(void)
{
    if (hide_data_count >= 10) {
        // 关闭加密文件
        if (hide_file_open) {
            f_close(&hide_file);
            hide_file_open = 0;
        }

        // 重置加密数据计数器和时间戳状态，为下一个加密文件做准备
        hide_data_count = 0;
        timestamp_initialized = 0;
    }
}

/**
 * @brief       ����ѹֵ����Ϊ8�ֽ�����
 * @param       voltage: ��ѹֵ
 * @param       buffer: ���ڴ洢��������8�ֽڻ�����
 * @retval      ��
 */
void encode_voltage(float voltage, uint8_t *buffer)
{
    // 将浮点数转换为32位整数表示
    union {
        float f;
        uint32_t i;
    } voltage_union;

    voltage_union.f = voltage;

    // 将32位整数拆分为4个字节
    buffer[0] = (voltage_union.i >> 24) & 0xFF;
    buffer[1] = (voltage_union.i >> 16) & 0xFF;
    buffer[2] = (voltage_union.i >> 8) & 0xFF;
    buffer[3] = voltage_union.i & 0xFF;

    // 后续4字节，可用于扩展或校验，暂时填充为0
    buffer[4] = 0x00;
    buffer[5] = 0x00;
    buffer[6] = 0x00;
    buffer[7] = 0x00;
}


/**
 * @brief       获取并显示SD卡信�?
 * @param       �?
 * @retval      �?
 */
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;      // SD卡信�?结构�?
    sd_error_enum status;                 // SD卡操作状�?
    uint32_t block_count, block_size;

    // 获取SD卡信�?
    status = sd_card_information_get(&sd_cardinfo);
    
    if(SD_OK == status)
    {
        my_printf(DEBUG_USART, "\r\n*** SD Card Info ***\r\n");
        
        // 显示卡类�?
        switch(sd_cardinfo.card_type)
        {
            case SDIO_STD_CAPACITY_SD_CARD_V1_1:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V1.1\r\n");
                break;
            case SDIO_STD_CAPACITY_SD_CARD_V2_0:
                my_printf(DEBUG_USART, "Card Type: Standard Capacity SD Card V2.0\r\n");
                break;
            case SDIO_HIGH_CAPACITY_SD_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity SD Card\r\n");
                break;
            case SDIO_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: Multimedia Card\r\n");
                break;
            case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Capacity Multimedia Card\r\n");
                break;
            case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
                my_printf(DEBUG_USART, "Card Type: High Speed Multimedia Card\r\n");
                break;
            default:
                my_printf(DEBUG_USART, "Card Type: Unknown\r\n");
                break;
        }
        
        // 计算块数量和大小
        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;
        block_size = 512;
        my_printf(DEBUG_USART,"\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        my_printf(DEBUG_USART,"\r\n## Block size is %dB ##", block_size);
        my_printf(DEBUG_USART,"\r\n## Block count is %d ##", block_count);
        
        // 显示制造商ID和其他信�?
        my_printf(DEBUG_USART, "Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        my_printf(DEBUG_USART, "OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);
        
        // 产品名称 (PNM)
        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';
        my_printf(DEBUG_USART, "Product Name: %s\r\n", pnm);
        
        // 产品版本和序列号
        my_printf(DEBUG_USART, "Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);
        // 产品序列号（32位数字）
        my_printf(DEBUG_USART, "Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);
        
        // 显示CSD版本信息
        my_printf(DEBUG_USART, "CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);
        
    }
    else
    {
        my_printf(DEBUG_USART, "\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

void sd_fatfs_test(void)
{
    uint16_t k = 5;
    DSTATUS stat = 0;
    do
    {
        stat = disk_initialize(0); 			//初�?�化SD卡，参数�?0，表示�??一�?磁盘驱动�?
    }while((stat != 0) && (--k));			//如果初�?�化失败，重试k�?
    
    card_info_get();
    
    my_printf(DEBUG_USART, "SD Card disk_initialize:%d\r\n",stat);
    f_mount(0, &fs);						 //挂载SD卡文件系统，驱动器号�?0
    my_printf(DEBUG_USART, "SD Card f_mount:%d\r\n",stat);
    
    if(RES_OK == stat)						 //�����ʼ���ɹ�
    {        
        my_printf(DEBUG_USART, "\r\nSD Card Initialize Success!\r\n");
     
        result = f_open(&fdst, "0:/FATFS.TXT", FA_CREATE_ALWAYS | FA_WRITE);		//��SD����Ŀ¼����FATFS.TXT�ļ�
     
        sprintf((char *)filebuffer, "HELLO MCUSTUDIO");

        //result = f_write(&fdst, textfilebuffer, sizeof(textfilebuffer), &bw); 	//将textfilebuffer内容写入文件
        result = f_write(&fdst, filebuffer, sizeof(filebuffer), &bw);				//将filebuffer内容写入文件

        /**********文件写入测试 begin****************/
        if(FR_OK == result)		
            my_printf(DEBUG_USART, "FATFS FILE write Success!\r\n");
        else
        {
            my_printf(DEBUG_USART, "FATFS FILE write failed!\r\n");
        }
        /**********文件写入测试 end****************/

        f_close(&fdst);//关闭文件


        f_open(&fdst, "0:/FATFS.TXT", FA_OPEN_EXISTING | FA_READ);	//打开已存在的文件进行读取
        br = 1;
        
        /**********文件读取测试 begin****************/
        for(;;)
        {
            // 清空缓冲区
            for (count=0; count<128; count++)
            {
                buffer[count]=0;
            }
            // 从文件读取数据到buffer
            result = f_read(&fdst, buffer, sizeof(buffer), &br);
            if ((0 == result)|| (0 == br))
            {
                break;
            }
        }
        /**********文件读取测试 end****************/

        // 比较读取的数据和写入的数据是否一致
        if(SUCCESS == memory_compare(buffer, filebuffer, 128))
        {
            my_printf(DEBUG_USART, "FATFS Read File Success!\r\nThe content is:%s\r\n",buffer);
        }
        else
        {
            my_printf(DEBUG_USART, "FATFS FILE read failed!\n");            
        }
         f_close(&fdst);//关闭文件
    }
}

void test_SD(void)
{
	 uint16_t k = 5;
    DSTATUS stat = 0;
    do
    {
        stat = disk_initialize(0); 			//初始化SD卡，参数为0，表示第一个磁盘驱动器
    }while((stat != 0) && (--k));			//如果初始化失败，重试k次

		if(stat)
		{
			my_printf(DEBUG_USART, "TF card........error\n");
			my_printf(DEBUG_USART, "can not find TF card\n");
		}
		else
		{
			my_printf(DEBUG_USART, "TF card........ok\n");
			my_printf(DEBUG_USART,"TF card memory: %dKB\n", sd_card_capacity_get());
       

		}
}


/**
 * @brief       �洢��������
 * @param       voltage: ��ѹֵ
 * @retval      ��
 */
void store_sample_data(float voltage)
{
    // 检查SD卡是否初始化成功
    if (!sd_init_success) {
        return;
    }

    // 加密模式下不存储到sample文件夹
    if (encrypt_mode_enabled) {
        return;
    }

    // 更新全局时间戳（如果需要的话）
    update_global_timestamp();

    /* 如果文件未打开，则创建新文件 */
    if (!sample_file_open) {
        sprintf(current_filename, "0:/sample/sampleData%s.txt", global_timestamp);

        f_mkdir("0:/sample");
        if (f_open(&sample_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE) == FR_OK) {
            sample_file_open = 1;
        }
    }

    /* 写入数据 */
    rtc_parameter_struct current_time;
    get_current_time(&current_time);

    // 将BCD格式转换为十进制
    uint8_t year_dec = bcd_to_decimal(current_time.year);
    uint8_t month_dec = bcd_to_decimal(current_time.month);
    uint8_t date_dec = bcd_to_decimal(current_time.date);
    uint8_t hour_dec = bcd_to_decimal(current_time.hour);
    uint8_t minute_dec = bcd_to_decimal(current_time.minute);
    uint8_t second_dec = bcd_to_decimal(current_time.second);

    char data_line[64];
    sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d %.2fV\r\n",
            year_dec, month_dec, date_dec,
            hour_dec, minute_dec, second_dec,
            voltage);

    UINT bw_local;
    if (sample_file_open) {
        f_write(&sample_file, data_line, strlen(data_line), &bw_local);
        f_sync(&sample_file);
    }

    sample_data_count++;
    global_data_count++;  // 保持全局计数器用于显示

    // 检查是否需要重置采样文件（在数据写入后检查）
    check_and_reset_sample_file();
}

/**
 * @brief       存储超限数据
 * @param       voltage: 电压值
 * @retval      无
 */
void store_overlimit_data(float voltage)
{
    // 检查SD卡是否初始化成功
    if (!sd_init_success) {
        return;
    }

    // 更新全局时间戳（如果需要的话）
    update_global_timestamp();

    /* 如果文件未打开，则创建新文件 */
    if (!overlimit_file_open) {
        sprintf(overlimit_filename, "0:/overLimit/overLimit%s.txt", global_timestamp);

        f_mkdir("0:/overLimit");
        if (f_open(&overlimit_file, overlimit_filename, FA_CREATE_ALWAYS | FA_WRITE) == FR_OK) {
            overlimit_file_open = 1;
        }
    }

    /* 写入数据 */
    rtc_parameter_struct current_time;
    get_current_time(&current_time);

    // 将BCD格式转换为十进制
    uint8_t year_dec = bcd_to_decimal(current_time.year);
    uint8_t month_dec = bcd_to_decimal(current_time.month);
    uint8_t date_dec = bcd_to_decimal(current_time.date);
    uint8_t hour_dec = bcd_to_decimal(current_time.hour);
    uint8_t minute_dec = bcd_to_decimal(current_time.minute);
    uint8_t second_dec = bcd_to_decimal(current_time.second);

    char data_line[128];
    sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV limit %.1fV\r\n",
            year_dec, month_dec, date_dec,
            hour_dec, minute_dec, second_dec,
            voltage, input_threshold);

    UINT bw_local;
    if (overlimit_file_open) {
        f_write(&overlimit_file, data_line, strlen(data_line), &bw_local);
        f_sync(&overlimit_file);
    }

    overlimit_data_count++;
    global_data_count++;  // 保持全局计数器用于显示

    // 检查是否需要重置超限文件（在数据写入后检查）
    check_and_reset_overlimit_file();
}

/**
 * @brief       存储日志条目
 * @param       action: 操作描述
 * @retval      无
 */
void store_log_entry(const char *action)
{
    // 检查SD卡是否初始化成功
    if (!sd_init_success) {
        return;
    }

    /* 初始化日志文件 */
    if (!log_initialized) {
        sprintf(current_filename, "0:/log/log%lu.txt", power_on_count - 1);  // ��0��ʼ����
        f_mkdir("0:/log");
        if (f_open(&log_file, current_filename, FA_CREATE_ALWAYS | FA_WRITE) == FR_OK) {
            log_file_open = 1;
            log_initialized = 1;
        }

        /* 写入系统启动信息 */
        if (log_file_open) {
            rtc_parameter_struct current_time;
            get_current_time(&current_time);

            // 将BCD格式转换为十进制
            uint8_t year_dec = bcd_to_decimal(current_time.year);
            uint8_t month_dec = bcd_to_decimal(current_time.month);
            uint8_t date_dec = bcd_to_decimal(current_time.date);
            uint8_t hour_dec = bcd_to_decimal(current_time.hour);
            uint8_t minute_dec = bcd_to_decimal(current_time.minute);
            uint8_t second_dec = bcd_to_decimal(current_time.second);

            char header[128];
            sprintf(header, "System started at 20%02d-%02d-%02d %02d:%02d:%02d (Power on count: %lu)\r\n",
                    year_dec, month_dec, date_dec,
                    hour_dec, minute_dec, second_dec, power_on_count);

            UINT bw_local;
            f_write(&log_file, header, strlen(header), &bw_local);
            f_sync(&log_file);
        }
    }

    /* 写入日志条目 */
    if (log_file_open) {
        rtc_parameter_struct current_time;
        get_current_time(&current_time);

        // 将BCD格式转换为十进制
        uint8_t year_dec = bcd_to_decimal(current_time.year);
        uint8_t month_dec = bcd_to_decimal(current_time.month);
        uint8_t date_dec = bcd_to_decimal(current_time.date);
        uint8_t hour_dec = bcd_to_decimal(current_time.hour);
        uint8_t minute_dec = bcd_to_decimal(current_time.minute);
        uint8_t second_dec = bcd_to_decimal(current_time.second);

        char log_entry[128];
        sprintf(log_entry, "[20%02d-%02d-%02d %02d:%02d:%02d] %s\r\n",
                year_dec, month_dec, date_dec,
                hour_dec, minute_dec, second_dec,
                action);

        UINT bw_local;
        f_write(&log_file, log_entry, strlen(log_entry), &bw_local);
        f_sync(&log_file);
    }
}

/**
 * @brief       存储加密数据
 * @param       timestamp: Unix时间戳
 * @param       voltage: 电压值
 * @retval      无
 */
void store_hide_data(uint32_t timestamp, float voltage)
{
    // 检查SD卡是否初始化成功
    if (!sd_init_success) {
        return;
    }

    // 更新全局时间戳（如果需要的话）
    update_global_timestamp();

    /* 如果文件未打开，则创建新文件 */
    if (!hide_file_open) {
        sprintf(hide_filename, "0:/hideData/hideData%s.txt", global_timestamp);

        f_mkdir("0:/hideData");
        if (f_open(&hide_file, hide_filename, FA_CREATE_ALWAYS | FA_WRITE) == FR_OK) {
            hide_file_open = 1;
        }
    }

    /* 获取当前时间用于显示 */
    rtc_parameter_struct current_time;
    get_current_time(&current_time);

    // 将BCD格式转换为十进制
    uint8_t year_dec = bcd_to_decimal(current_time.year);
    uint8_t month_dec = bcd_to_decimal(current_time.month);
    uint8_t date_dec = bcd_to_decimal(current_time.date);
    uint8_t hour_dec = bcd_to_decimal(current_time.hour);
    uint8_t minute_dec = bcd_to_decimal(current_time.minute);
    uint8_t second_dec = bcd_to_decimal(current_time.second);

    /* 存储加密数据：原始数据 + 加密数据 */
    uint32_t voltage_hex = voltage_to_hex(voltage);
    char data_line[128];

    // 检查是否超限，超限数据在加密数据后添加*标记
    if(voltage > input_threshold) {
        sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV\r\nhide: %08lX%08lX*\r\n",
                year_dec, month_dec, date_dec,
                hour_dec, minute_dec, second_dec,
                voltage, timestamp, voltage_hex);
    } else {
        sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d %.1fV\r\nhide: %08lX%08lX\r\n",
                year_dec, month_dec, date_dec,
                hour_dec, minute_dec, second_dec,
                voltage, timestamp, voltage_hex);
    }

    UINT bw_local;
    if (hide_file_open) {
        f_write(&hide_file, data_line, strlen(data_line), &bw_local);
        f_sync(&hide_file);
    }

    hide_data_count++;
    global_data_count++;  // 保持全局计数器用于显示

    // 检查是否需要重置加密文件（在数据写入后检查）
    check_and_reset_hide_file();
}

/**
 * @brief       重置数据存储系统
 * @param       none
 * @retval      none
 * @note        用于手动停止采样时重置文件管理状态
 */
void reset_data_storage_system(void)
{
    // 关闭所有打开的文件
    if (sample_file_open) {
        f_close(&sample_file);
        sample_file_open = 0;
    }
    if (overlimit_file_open) {
        f_close(&overlimit_file);
        overlimit_file_open = 0;
    }
    if (hide_file_open) {
        f_close(&hide_file);
        hide_file_open = 0;
    }

    // 重置所有计数器和时间戳状态
    sample_data_count = 0;
    overlimit_data_count = 0;
    hide_data_count = 0;
    global_data_count = 0;
    timestamp_initialized = 0;
}

/**
 * @brief       处理加密模式切换
 * @param       none
 * @retval      none
 * @note        当加密模式切换时，重置时间戳以确保新文件使用新的时间戳
 */
void handle_encryption_mode_change(void)
{
    // 重置时间戳状态，这样下次创建文件时会使用新的时间戳
    timestamp_initialized = 0;
}

void read_config_file(void)
{
    FIL file;
    FRESULT res;
    char line[64];
    uint8_t in_ratio_section = 0;
    uint8_t in_limit_section = 0;

    // 检查SD卡是否初始化成功
    if (!sd_init_success) {
        my_printf(DEBUG_USART, "SD card not initialized\r\n");
        return;
    }

    // 打开SD卡根目录下的config.ini文件
    res = f_open(&file, "0:/config.ini", FA_READ);
    if (res != FR_OK) {
        my_printf(DEBUG_USART, "config.ini file not found.\r\n");
        return;
    }
		
		my_printf(DEBUG_USART, "config.ini\n");
		
    // 逐行读取文件内容
    while (f_gets(line, sizeof(line), &file)) {
        // 去除行尾的换行符
        char *newline = strchr(line, '\n');
        if (newline) *newline = '\0';
        char *carriage = strchr(line, '\r');
        if (carriage) *carriage = '\0';

        // 检查是否进入[Ratio]段
        if (strcmp(line, "[Ratio]") == 0) {
            in_ratio_section = 1;
            in_limit_section = 0;
            continue;
        }
        // 检查是否进入[Limit]段
        else if (strcmp(line, "[Limit]") == 0) {
            in_ratio_section = 0;
            in_limit_section = 1;
            continue;
        }
        // 检查是否进入其他段（重置标志）
        else if (line[0] == '[') {
            in_ratio_section = 0;
            in_limit_section = 0;
            continue;
        }

        // 在[Ratio]段中读取Ch0值
				
        if (in_ratio_section && strstr(line, "Ch0")) {
            float ratio_value;
            if (sscanf(line, "Ch0 = %f", &ratio_value) == 1) {
							my_printf(DEBUG_USART, "[Ratio]\n");
                float t_ratio = ratio_value;
                my_printf(DEBUG_USART, "Ch0 = %.2f\r\n", t_ratio);
            }
        }
        // 在[Limit]段中读取Ch0值
        else if (in_limit_section && strstr(line, "Ch0")) {
            float limit_value;
            if (sscanf(line, "Ch0 = %f", &limit_value) == 1) {
								my_printf(DEBUG_USART, "[Limit]\n");
                float t_limit= limit_value;
                my_printf(DEBUG_USART, "Ch0 = %.2f\r\n", t_limit);
            }
        }
    }

    f_close(&file);
    

    // 记录配置读取日志
    store_log_entry("config file read from SD card");
}

