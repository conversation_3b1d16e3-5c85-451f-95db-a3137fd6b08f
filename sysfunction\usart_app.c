

#include "bsp_gd32f470vet6.h"
#include "uart_ringbuffer.h"
#include "flash.app.h"
#include "sd_app.h"

uint8_t mode = 0;
__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t radio_Num = 0;
uint8_t limit_Num = 0;
uint8_t temp_buffer[128];
// 使用DMA方式进行串口数据传输的缓冲区
// 这里暂时注释掉，使用其他方式
//extern uint8_t uart_dma_buffer[512] = {0};

// 配置相关函数的实现在flash.app.c中

// RTC配置状态管理
static uint8_t rtc_config_waiting = 0;  // 0=不等待, 1=等待时间输入

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;
    // 格式化字符串到缓冲区
    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
    
    return len;
}


//读取并显示配置
//void flash_ratio_threshold_readwrite(void)
//{
//    // 从Flash读取当前存储的配置值
//    float current_ratio;
//    float current_threshold;

//    current_ratio = flash_ratio_read();
//    current_threshold = flash_threshold_read();

//    my_printf(DEBUG_USART, "Current stored values:\r\n");
//    my_printf(DEBUG_USART, "Ratio = %.2f\r\n", current_ratio);
//    my_printf(DEBUG_USART, "Threshold = %.2f\r\n", current_threshold);
//}

void parse_uart_command(uint8_t *buffer,uint16_t length)
{
	 uint8_t cycle = (KeyNum - 1)*5;
    // 处理START命令，开始采样
    if(strcmp((char *)temp_buffer, "START") == 0 )
    {
		
		KeyNum = flash_keynum_read_silent();
		
		uint8_t cycle = (KeyNum - 1)*5;
		
        timer_enable(TIMER4);
			timer_enable(TIMER6);
			
			OLED_Clear();
			
			my_printf(DEBUG_USART, "Periodic Sampling\r\n");
			my_printf(DEBUG_USART, "sample cycle: %d\r\n", cycle);

			oled_flag = 1;

        // 记录启动日志
        store_log_entry("sample start - cycle 5s (command)");


    }
    // 处理STOP命令，停止采样
    else if(strcmp((char *)temp_buffer, "STOP") == 0 )
    {
        my_printf(DEBUG_USART, "Periodic Sampling STOP\r\n");

				OLED_Clear();

				oled_flag=0;

				timer_disable(TIMER4);

				timer_disable(TIMER6);  // 停止定时器

				LED1_OFF;

				// 记录停止日志
				store_log_entry("sample stop (command)");

				// 重置数据存储系统
				reset_data_storage_system();

    }
	// RTC配置命令处理
	else if (strcmp((char *)temp_buffer, "RTC Config") == 0)
	{
		// 记录RTC配置开始
		store_log_entry("rtc config");

		// 设置等待时间输入状态
		rtc_config_waiting = 1;

		// 提示用户输入时间
		my_printf(DEBUG_USART, "Input Datetime\r\n");
	}

	else if(strcmp((char *)temp_buffer, "RTC now") == 0)
    {
        
		// 显示当前RTC时间
		my_printf(DEBUG_USART, "Current time:");
        rtc_show_time();
    }
		else if(strcmp((char *)temp_buffer, "test") == 0)
    {
        my_printf(DEBUG_USART, "\n=====system selftest=====\r\n");
				test_flash();
				test_SD();
			
				my_printf(DEBUG_USART, "RTC:");
				rtc_show_time();
    }
	else if(strcmp((char *)temp_buffer, "radio") == 0)
    {
        // 显示当前变比值
        float current_ratio;
		if(radio_Num == 0)
		{
			current_ratio = flash_ratio_read();
		}
		else
		{
			current_ratio = input_radio;
		}	
			my_printf(DEBUG_USART, "radio = %.2f\r\n", current_ratio);

			my_printf(DEBUG_USART, "Input value(0~100)\r\n");
			store_log_entry("ratio config");
			config_set_waiting_ratio_input(); // 设置等待输入标志
		
    }
	
	else if(strcmp((char *)temp_buffer, "limit") == 0)
    {
        // 显示当前阈值
        float current_limit;
		if(limit_Num == 0)
		{
			current_limit = flash_threshold_read();
		}
		else
		{
			current_limit = input_threshold;
		}
			my_printf(DEBUG_USART, "limit = %.2f\r\n", current_limit);
			my_printf(DEBUG_USART, "Input value(0~200)\r\n");
			store_log_entry("limit config");
			config_set_waiting_limit_input(); // 设置等待输入标志
		
    }
	
	else if(strcmp((char *)temp_buffer, "hide") == 0)
    {
        encrypt_mode_enabled = 1;
        my_printf(DEBUG_USART, "Data encryption enabled\r\n");
        store_log_entry("hide data");
        handle_encryption_mode_change();  // 处理加密模式切换
    }
	else if(strcmp((char *)temp_buffer, "unhide") == 0)
    {
        encrypt_mode_enabled = 0;
        my_printf(DEBUG_USART, "Data encryption disabled\r\n");
        store_log_entry("unhide data");
        handle_encryption_mode_change();  // 处理加密模式切换
    }
	else if(strcmp((char *)temp_buffer, "power count") == 0)
    {
        my_printf(DEBUG_USART, "Power on count: %lu\r\n", power_on_count);
    }
	else if(strcmp((char *)temp_buffer, "data count") == 0)
    {
        extern uint16_t global_data_count;
        my_printf(DEBUG_USART, "Current data count: %d/10\r\n", global_data_count);
    }
	else if(strcmp((char *)temp_buffer, "config save") == 0)
    {
        flash_ratio_stored(input_value);
		
		flash_threshold_stored(input_threshold);
		
		my_printf(DEBUG_USART, "ratio = %.2f\r\n", input_value);
		
		my_printf(DEBUG_USART, "limit = %.2f\r\n", input_threshold);
		
		my_printf(DEBUG_USART, "save parameters flash\r\n");
    }
	
	else if(strcmp((char *)temp_buffer, "config read") == 0)
    {
		float current_ratio;
        current_ratio = flash_ratio_read();
		
		float current_limit;
        current_limit = flash_ratio_read();
        
        my_printf(DEBUG_USART, "read parameters from flash\r\n");
		
		my_printf(DEBUG_USART, "radio = %.2f\r\n", current_ratio);
		
		my_printf(DEBUG_USART, "limit = %.2f\r\n", current_limit);
    }
		
		else if (strcmp((char *)temp_buffer, "conf") == 0) {
								
                read_config_file();
            } 
		else if (strcmp((char *)temp_buffer, "zero") == 0) {
			my_printf(DEBUG_USART, "清零前power_on_count: %lu\r\n", power_on_count);

			// 执行临时清零操作（仅清零内存，重启后恢复）
			power_on_count_reset();

			my_printf(DEBUG_USART, "清零后power_on_count: %lu\r\n", power_on_count);

            }
		else if (strcmp((char *)temp_buffer, "zero permanent") == 0) {
			my_printf(DEBUG_USART, "清零前power_on_count: %lu\r\n", power_on_count);

			// 执行彻底清零操作（清零Flash，重启后从1开始计数）
			power_on_count_reset_permanent();

			my_printf(DEBUG_USART, "清零后power_on_count: %lu\r\n", power_on_count);

            }


	else if (rtc_config_waiting == 1)
	{
		// 处理RTC时间输入
		RTC_StatusTypeDef status = rtc_set_time_from_string_ex((char *)temp_buffer);

		if (status == RTC_STATUS_OK)
		{
			// 设置成功
			my_printf(DEBUG_USART, "RTC Config success\r\n");
			my_printf(DEBUG_USART, "Time: %s\r\n", temp_buffer);

			// 记录RTC配置成功
			char log_msg[64];
			sprintf(log_msg, "rtc config success to %s", temp_buffer);
			store_log_entry(log_msg);
		}
		else
		{
			// 设置失败
			my_printf(DEBUG_USART, "RTC Config failed\r\n");
			store_log_entry("rtc config failed");
		}

		// 重置等待状态
		rtc_config_waiting = 0;
	}
	else if (config_is_waiting_input())
	{
		// 处理用户输入的配置数据
		if (config_process_input(temp_buffer))
		{
			// 输入处理成功
		}
		else
		{
			my_printf(DEBUG_USART, "\r\n");
		}
	}
	else
	{
		my_printf(DEBUG_USART, "未知命令\r\n");
	}
}

void uart_task(void)
{
    uint32_t read_size;
    
	
	
    if(!rx_flag) return;
    
    // 清除接收标志
    rx_flag = 0;

    // 检查环形缓冲区是否有数据可读
    if(uart_ringbuffer_available() > 0)
    {
        // 从环形缓冲区读取数据，最多读取128字节
        read_size = uart_ringbuffer_read(temp_buffer, sizeof(temp_buffer));

        if(read_size > 0)
        {
            // 确保字符串以null结尾
            if(read_size < sizeof(temp_buffer))
            {
                temp_buffer[read_size] = '\0';

            }
            else
            {
                temp_buffer[sizeof(temp_buffer) - 1] = '\0';
            }

            // 可选：回显接收到的数据到串口
//            my_printf(DEBUG_USART, "%s", temp_buffer);

        }
    }
		parse_uart_command(temp_buffer,read_size);
		
}
















