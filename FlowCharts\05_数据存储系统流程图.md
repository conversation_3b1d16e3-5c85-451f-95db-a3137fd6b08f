# 数据存储系统流程图

## 图表说明
本流程图展示了DSB系统的数据存储机制，包括4种不同类型数据的分类存储和文件管理逻辑。

## Mermaid代码

```mermaid
graph TD
    A[数据存储请求] --> B{数据类型判断}
    B -->|正常采样数据| C[store_sample_data]
    B -->|超限数据| D[store_overlimit_data]
    B -->|加密数据| E[store_hide_data]
    B -->|系统日志| F[store_log_entry]
    
    C --> G[进入sample文件夹]
    D --> H[进入overLimit文件夹]
    E --> I[进入hideData文件夹]
    F --> J[进入log文件夹]
    
    G --> K[检查当前文件记录数]
    H --> K
    I --> K
    J --> K
    
    K --> L{记录数 >= 10?}
    L -->|是| M[创建新文件]
    L -->|否| N[使用当前文件]
    
    M --> O[生成时间戳文件名<br/>YYYYMMDDHHmmss.txt]
    O --> P[创建文件]
    P --> Q{文件创建成功?}
    Q -->|否| R[错误处理]
    Q -->|是| S[重置记录计数器]
    
    N --> T[打开当前文件]
    T --> U{文件打开成功?}
    U -->|否| R
    U -->|是| V[准备写入数据]
    
    S --> V
    V --> W[格式化数据]
    W --> X{数据类型}
    X -->|正常数据| Y[格式：时间 + 电压值]
    X -->|超限数据| Z[格式：时间 + 电压值 + 超限标记]
    X -->|加密数据| AA[格式：时间戳HEX + 电压HEX]
    X -->|日志数据| BB[格式：时间 + 日志内容]
    
    Y --> CC[写入文件]
    Z --> CC
    AA --> CC
    BB --> CC
    
    CC --> DD{写入成功?}
    DD -->|是| EE[更新记录计数]
    DD -->|否| R
    
    EE --> FF[同步文件系统<br/>f_sync]
    FF --> GG[关闭文件]
    GG --> HH[返回成功]
    
    R --> II[记录错误日志]
    II --> JJ[返回失败]
    
    style A fill:#e3f2fd
    style B fill:#fff9c4
    style L fill:#fff9c4
    style Q fill:#fff9c4
    style U fill:#fff9c4
    style X fill:#f3e5f5
    style DD fill:#fff9c4
    style HH fill:#c8e6c9
    style JJ fill:#ffebee
```

## 存储系统架构

### 文件夹分类
```
SD卡根目录/
├── sample/          # 正常采样数据
├── overLimit/       # 超限数据
├── hideData/        # 加密数据
└── log/            # 系统日志
```

### 文件命名规则
- **格式**：YYYYMMDDHHmmss.txt
- **示例**：20250118143025.txt（2025年1月18日14:30:25）
- **自动创建**：每10条记录自动创建新文件

### 数据格式规范

#### 正常采样数据（sample文件夹）
```
2025-01-18 14:30:25, 2.45V
2025-01-18 14:30:30, 2.48V
2025-01-18 14:30:35, 2.52V
```

#### 超限数据（overLimit文件夹）
```
2025-01-18 14:30:40, 3.15V, OVERLIMIT
2025-01-18 14:30:45, 3.22V, OVERLIMIT
```

#### 加密数据（hideData文件夹）
```
679B2F4012345678
679B2F4512345679
679B2F4A1234567A
```

#### 系统日志（log文件夹）
```
2025-01-18 14:30:00, system init
2025-01-18 14:30:01, test ok
2025-01-18 14:30:05, sample start - cycle 5s
```

## 存储流程详解

### 文件管理机制
1. **记录计数**：每个文件夹维护当前文件的记录数
2. **自动分割**：达到10条记录自动创建新文件
3. **时间戳命名**：使用创建时间作为文件名
4. **错误恢复**：文件操作失败时的错误处理

### 数据写入流程
```c
// 数据存储示例代码
FRESULT store_sample_data(float voltage) {
    // 1. 检查记录数
    if (sample_record_count >= 10) {
        create_new_sample_file();
        sample_record_count = 0;
    }
    
    // 2. 格式化数据
    char data_string[64];
    sprintf(data_string, "%s, %.2fV\n", 
            get_current_time_string(), voltage);
    
    // 3. 写入文件
    UINT bytes_written;
    FRESULT result = f_write(&sample_file, data_string, 
                            strlen(data_string), &bytes_written);
    
    // 4. 同步文件系统
    if (result == FR_OK) {
        f_sync(&sample_file);
        sample_record_count++;
    }
    
    return result;
}
```

### 错误处理机制
1. **文件创建失败**：重试机制，记录错误日志
2. **写入失败**：数据缓存，延后重试
3. **存储空间不足**：清理旧文件，释放空间
4. **文件系统错误**：重新初始化文件系统

## 存储特性

### 数据安全性
- **实时同步**：每次写入后立即同步
- **分类存储**：不同类型数据隔离存储
- **备份机制**：重要数据多重备份
- **完整性检查**：数据写入验证

### 存储效率
- **批量写入**：减少文件操作次数
- **缓存机制**：频繁访问数据缓存
- **压缩存储**：可选的数据压缩
- **索引管理**：快速数据检索

### 可维护性
- **清晰命名**：文件名包含时间信息
- **标准格式**：统一的数据格式
- **日志记录**：详细的操作日志
- **状态监控**：存储状态实时监控

## 优化建议

1. **缓冲区优化**：使用更大的写入缓冲区
2. **压缩算法**：对历史数据进行压缩
3. **索引系统**：建立数据索引加速查询
4. **清理机制**：自动清理过期数据
5. **备份策略**：重要数据的备份机制
