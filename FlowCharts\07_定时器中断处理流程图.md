# 定时器中断处理流程图

## 图表说明
本流程图展示了TIMER6中断处理函数的完整逻辑，包括不同采样周期的控制和标志位设置机制。

## Mermaid代码

```mermaid
graph TD
    A[TIMER6中断触发] --> B[检查中断标志位<br/>TIMER_INT_FLAG_UP]
    B --> C{标志位有效?}
    C -->|否| D[退出中断]
    C -->|是| E[清除中断标志位]
    
    E --> F[周期计数器递增<br/>cycle_counter++]
    F --> G{采样周期判断<br/>KeyNum值}
    
    G -->|KeyNum=2<br/>5秒周期| H[每个周期都执行<br/>should_execute = 1]
    G -->|KeyNum=3<br/>10秒周期| I{cycle_counter >= 2?}
    G -->|KeyNum=4<br/>15秒周期| J{cycle_counter >= 3?}
    
    I -->|是| K[设置执行标志<br/>should_execute = 1]
    I -->|否| L[不执行<br/>should_execute = 0]
    
    J -->|是| M[设置执行标志<br/>should_execute = 1]
    J -->|否| N[不执行<br/>should_execute = 0]
    
    H --> O[重置计数器<br/>cycle_counter = 0]
    K --> P[重置计数器<br/>cycle_counter = 0]
    M --> Q[重置计数器<br/>cycle_counter = 0]
    L --> R[保持计数器]
    N --> S[保持计数器]
    
    O --> T{should_execute == 1?}
    P --> T
    Q --> T
    R --> T
    S --> T
    
    T -->|是| U[设置ADC处理标志<br/>timer6_execute_flag = 1]
    T -->|否| V[不设置标志]
    
    U --> W[退出中断]
    V --> W
    W --> X[返回主程序]
    
    X --> Y[主循环中adc_process_task检查标志]
    Y --> Z{timer6_execute_flag == 1?}
    Z -->|是| AA[执行ADC数据处理]
    Z -->|否| BB[跳过处理]
    
    AA --> CC[ADC数据读取和处理]
    CC --> DD[清除标志位<br/>timer6_execute_flag = 0]
    DD --> EE[继续主循环]
    BB --> EE
    
    style A fill:#e3f2fd
    style C fill:#fff9c4
    style G fill:#f3e5f5
    style I fill:#fff9c4
    style J fill:#fff9c4
    style T fill:#fff9c4
    style Z fill:#fff9c4
    style U fill:#c8e6c9
    style AA fill:#ffeb3b
```

## 定时器中断机制

### TIMER6配置
```c
// TIMER6配置为5秒基础周期
void timer6_config(void) {
    timer_parameter_struct timer_initpara;
    
    // 使能TIMER6时钟
    rcu_periph_clock_enable(RCU_TIMER6);
    
    // 配置定时器参数
    timer_initpara.prescaler = 23999;        // 预分频器
    timer_initpara.alignedmode = TIMER_COUNTER_EDGE;
    timer_initpara.counterdirection = TIMER_COUNTER_UP;
    timer_initpara.period = 9999;            // 自动重装载值
    timer_initpara.clockdivision = TIMER_CKDIV_DIV1;
    
    timer_init(TIMER6, &timer_initpara);
    
    // 使能中断
    timer_interrupt_enable(TIMER6, TIMER_INT_UP);
    nvic_irq_enable(TIMER6_IRQn, 2, 0);
}
```

### 中断处理函数
```c
void TIMER6_IRQHandler(void)
{
    static uint8_t cycle_counter = 0;
    
    if (timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP) == SET) {
        // 清除中断标志位
        timer_interrupt_flag_clear(TIMER6, TIMER_INT_FLAG_UP);
        
        cycle_counter++;
        uint8_t should_execute = 0;
        
        // 根据KeyNum值判断采样周期
        if (KeyNum == 2) {
            // 5秒周期：每个周期都执行
            should_execute = 1;
            cycle_counter = 0;
        } else if (KeyNum == 3) {
            // 10秒周期：每两个周期执行一次
            if (cycle_counter >= 2) {
                should_execute = 1;
                cycle_counter = 0;
            }
        } else if (KeyNum == 4) {
            // 15秒周期：每三个周期执行一次
            if (cycle_counter >= 3) {
                should_execute = 1;
                cycle_counter = 0;
            }
        }
        
        // 设置ADC处理标志
        if (should_execute) {
            timer6_execute_flag = 1;
        }
    }
}
```

## 采样周期控制

### 周期配置表
| KeyNum | 采样周期 | cycle_counter阈值 | 说明 |
|--------|----------|-------------------|------|
| 2 | 5秒 | 1 | 每个5秒周期都执行 |
| 3 | 10秒 | 2 | 每两个5秒周期执行一次 |
| 4 | 15秒 | 3 | 每三个5秒周期执行一次 |

### 时序图
```
5秒周期 (KeyNum=2):
Timer: |--5s--|--5s--|--5s--|--5s--|
ADC:   |  ✓   |  ✓   |  ✓   |  ✓   |

10秒周期 (KeyNum=3):
Timer: |--5s--|--5s--|--5s--|--5s--|
ADC:   |      |  ✓   |      |  ✓   |

15秒周期 (KeyNum=4):
Timer: |--5s--|--5s--|--5s--|--5s--|--5s--|--5s--|
ADC:   |      |      |  ✓   |      |      |  ✓   |
```

## 中断与主循环协调

### 标志位机制
1. **中断设置标志**：timer6_execute_flag = 1
2. **主循环检查标志**：adc_process_task()中检查
3. **处理后清除标志**：timer6_execute_flag = 0
4. **避免重复处理**：标志位确保数据只处理一次

### 实时性保证
- **中断优先级**：TIMER6中断优先级设置为2
- **快速处理**：中断中只设置标志位，复杂处理在主循环
- **无阻塞**：中断处理时间极短，不影响系统响应

### 数据同步
```c
// 中断中设置标志
void TIMER6_IRQHandler(void) {
    // ... 周期判断逻辑 ...
    if (should_execute) {
        timer6_execute_flag = 1;  // 原子操作
    }
}

// 主循环中检查标志
void adc_process_task(void) {
    if (timer6_execute_flag == 1) {
        timer6_execute_flag = 0;  // 立即清除标志
        
        // 执行ADC数据处理
        adc_val = adc_value[0];
        Vol_Value = input_radio * adc_val * 3.3 / 4095;
        // ... 其他处理 ...
    }
}
```

## 设计优势

### 灵活的周期控制
- **基础周期**：5秒作为基础时间单位
- **倍数关系**：10秒和15秒都是5秒的倍数
- **动态切换**：运行时可以切换采样周期
- **精确控制**：硬件定时器保证时间精度

### 系统效率
- **中断简短**：中断处理时间最短
- **主循环处理**：复杂逻辑在主循环中处理
- **避免丢失**：标志位机制避免数据丢失
- **资源节约**：不需要为每个周期配置单独定时器

### 可维护性
- **清晰逻辑**：周期控制逻辑简单明了
- **易于扩展**：添加新周期只需修改判断条件
- **调试友好**：标志位状态易于观察
- **文档完整**：详细的注释和说明

## 潜在优化

1. **周期参数化**：将周期值存储在配置表中
2. **动态调整**：支持任意周期值设置
3. **精度提升**：使用更高精度的定时器
4. **功耗优化**：空闲时降低定时器频率
