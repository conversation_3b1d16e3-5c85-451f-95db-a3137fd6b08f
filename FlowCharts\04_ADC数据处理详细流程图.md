# ADC数据处理详细流程图

## 图表说明
本流程图详细展示了adc_process_task()函数的完整处理逻辑，包括数据采集、处理、存储的全过程。

## Mermaid代码

```mermaid
graph TD
    A[adc_process_task开始] --> B{timer6_execute_flag == 1?}
    B -->|否| C[返回，等待下次调用]
    B -->|是| D[清除标志位<br/>timer6_execute_flag = 0]
    
    D --> E[读取ADC原始值<br/>adc_val = adc_value[0]]
    E --> F[电压转换<br/>Vol_Value = input_radio * adc_val * 3.3 / 4095]
    F --> G[OLED显示电压值<br/>oled_printf]
    
    G --> H{加密模式启用?<br/>encrypt_mode_enabled}
    H -->|是| I[加密模式处理]
    H -->|否| J[正常模式处理]
    
    I --> K[获取时间戳<br/>rtc_to_unix_timestamp_hex]
    K --> L[存储到hideData文件夹<br/>store_hide_data]
    L --> M[电压值转换为HEX<br/>voltage_to_hex]
    M --> N{电压超限检测<br/>Vol_Value > input_threshold}
    N -->|是| O[输出加密数据+超限标记<br/>printf HEX*]
    N -->|否| P[输出加密数据<br/>printf HEX]
    O --> Q[超限处理流程]
    P --> R[正常处理流程]
    
    J --> S[正常模式数据处理]
    S --> T{电压超限检测<br/>Vol_Value > input_threshold}
    
    T -->|是| Q
    T -->|否| R
    
    Q --> U[点亮超限LED<br/>LED2_ON]
    U --> V[显示当前时间<br/>rtc_adcshow_time]
    V --> W[输出超限信息<br/>printf OverLimit]
    W --> X[存储超限数据<br/>store_overlimit_data]
    X --> Y[返回主循环]
    
    R --> Z[关闭超限LED<br/>LED2_OFF]
    Z --> AA[显示当前时间<br/>rtc_adcshow_time]
    AA --> BB[输出正常信息<br/>printf 正常电压]
    BB --> CC[存储正常数据<br/>store_sample_data]
    CC --> Y
    
    style A fill:#e3f2fd
    style B fill:#fff9c4
    style H fill:#fff9c4
    style N fill:#fff9c4
    style T fill:#fff9c4
    style Q fill:#ffebee
    style R fill:#e8f5e8
    style Y fill:#f3e5f5
```

## 数据处理流程详解

### 触发条件检查
- **标志位检查**：timer6_execute_flag由定时器中断设置
- **周期控制**：根据KeyNum值控制采样周期（5s/10s/15s）
- **标志位清除**：处理开始时立即清除标志位

### ADC数据获取和转换
```c
// ADC原始值读取
adc_val = adc_value[0];  // DMA自动更新的ADC值

// 电压转换公式
Vol_Value = input_radio * adc_val * 3.3 / 4095;
// input_radio: 变比系数（0-100）
// adc_val: 12位ADC原始值（0-4095）
// 3.3: 参考电压
// 4095: 12位ADC最大值
```

### 加密模式处理
当encrypt_mode_enabled=1时：
1. **时间戳获取**：获取Unix时间戳的十六进制格式
2. **数据加密**：电压值转换为十六进制格式
3. **加密存储**：数据存储到hideData文件夹
4. **加密输出**：串口输出十六进制格式数据

### 超限检测逻辑
```c
if (Vol_Value > input_threshold) {
    // 超限处理
    LED2_ON;                    // 点亮报警LED
    rtc_adcshow_time();         // 显示时间
    printf("OverLimit!");       // 输出超限信息
    store_overlimit_data();     // 存储超限数据
} else {
    // 正常处理
    LED2_OFF;                   // 关闭报警LED
    rtc_adcshow_time();         // 显示时间
    printf("Normal");           // 输出正常信息
    store_sample_data();        // 存储正常数据
}
```

### 数据存储分类
1. **正常数据**：存储到sample文件夹
2. **超限数据**：存储到overLimit文件夹
3. **加密数据**：存储到hideData文件夹
4. **系统日志**：存储到log文件夹

## 关键特性

### 实时性保证
- **1ms周期检查**：确保及时处理采样数据
- **DMA自动更新**：ADC值由DMA自动更新，无需CPU干预
- **标志位机制**：中断设置标志，主循环处理，避免中断中复杂处理

### 数据安全性
- **加密存储**：支持数据加密模式
- **分类存储**：不同类型数据分别存储
- **超限报警**：实时检测和报警机制

### 用户交互
- **OLED显示**：实时显示电压值
- **LED指示**：超限状态LED指示
- **串口输出**：详细的数据和状态信息

### 错误处理
- **数据验证**：ADC值范围检查
- **存储错误处理**：文件操作失败处理
- **状态恢复**：异常情况下的状态恢复

## 性能优化点

1. **预计算系数**：电压转换系数可预计算
2. **批量存储**：多条数据批量写入文件
3. **缓存机制**：频繁访问的数据使用缓存
4. **中断优化**：减少中断处理时间
