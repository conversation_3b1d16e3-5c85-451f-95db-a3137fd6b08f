.\output\uart_ringbuffer.o: ..\sysfunction\uart_ringbuffer.c
.\output\uart_ringbuffer.o: .\RTE\_McuSTUDIO_F470VET6\Pre_Include_Global.h
.\output\uart_ringbuffer.o: ..\sysfunction\uart_ringbuffer.h
.\output\uart_ringbuffer.o: ..\Components\ringbuffer\ringbuffer.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\assert.h
.\output\uart_ringbuffer.o: ..\Components\bsp\bsp_gd32f470vet6.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include\core_cm4.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_version.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_compiler.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include\cmsis_armcc.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS\5.9.0\CMSIS\Core\Include\mpu_armv7.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\output\uart_ringbuffer.o: ..\USER\inc\gd32f4xx_libopt.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_rcu.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_adc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_can.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_crc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_ctc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_dac.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_dbg.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_dci.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_dma.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_exti.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_fmc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_fwdgt.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_gpio.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_syscfg.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_i2c.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_iref.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_pmu.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_rtc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_sdio.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_spi.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_timer.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_trng.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_usart.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_wwdgt.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_misc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_enet.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_exmc.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_ipa.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Libraries\Include\gd32f4xx_tli.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\USER\inc\systick.h
.\output\uart_ringbuffer.o: ..\Components\ebtn\ebtn.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\output\uart_ringbuffer.o: ..\Components\ebtn\bit_array.h
.\output\uart_ringbuffer.o: ..\Components\oled\oled.h
.\output\uart_ringbuffer.o: ..\Components\gd25qxx\gd25qxx.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Components\sdio\sdio_sdcard.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\Components\fatfs\ff.h
.\output\uart_ringbuffer.o: ..\Components\fatfs\integer.h
.\output\uart_ringbuffer.o: ..\Components\fatfs\ffconf.h
.\output\uart_ringbuffer.o: ..\Components\fatfs\diskio.h
.\output\uart_ringbuffer.o: ..\sysfunction\sd_app.h
.\output\uart_ringbuffer.o: ..\sysfunction\led_app.h
.\output\uart_ringbuffer.o: ..\sysfunction\adc_app.h
.\output\uart_ringbuffer.o: ..\sysfunction\oled_app.h
.\output\uart_ringbuffer.o: ..\sysfunction\usart_app.h
.\output\uart_ringbuffer.o: ..\Driver\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\output\uart_ringbuffer.o: ..\sysfunction\btn_app.h
.\output\uart_ringbuffer.o: ..\sysfunction\scheduler.h
.\output\uart_ringbuffer.o: ..\sysfunction\rtc_app.h
.\output\uart_ringbuffer.o: ..\Components\bsp\bsp_gd32f470vet6.h
.\output\uart_ringbuffer.o: ..\sysfunction\flash.app.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\GorgonMeducer\perf_counter\2.4.0\perf_counter.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\Packs\GorgonMeducer\perf_counter\2.4.0\perfc_port_default.h
.\output\uart_ringbuffer.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdarg.h
