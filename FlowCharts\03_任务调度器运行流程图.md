# 任务调度器运行流程图

## 图表说明
本流程图展示了scheduler_run()函数的详细执行逻辑，包括5个不同周期任务的调度和执行过程。

## Mermaid代码

```mermaid
graph TD
    A[scheduler_run开始] --> B[初始化任务索引 i=0]
    B --> C{i < task_num?}
    C -->|是| D[获取当前系统时间<br/>get_system_ms]
    C -->|否| A
    
    D --> E{当前时间 >= <br/>任务周期 + 上次运行时间?}
    E -->|是| F[更新任务上次运行时间]
    E -->|否| G[i++]
    
    F --> H[执行任务函数<br/>task_func]
    H --> I{任务类型判断}
    
    I -->|adc_process_task<br/>1ms| J[ADC数据处理]
    I -->|btn_task<br/>5ms| K[按键处理]
    I -->|uart_task<br/>5ms| L[串口处理]
    I -->|adc_flip_task<br/>5ms| M[ADC控制]
    I -->|rtc_task<br/>50ms| N[RTC显示]
    
    J --> O[检查timer6_execute_flag]
    O -->|标志位=1| P[执行ADC数据处理]
    O -->|标志位=0| G
    P --> Q[读取ADC值]
    Q --> R[电压转换]
    R --> S[超限检测]
    S --> T[数据存储]
    T --> U[OLED显示]
    U --> G
    
    K --> V[扫描所有按键]
    V --> W{按键按下?}
    W -->|KEY3| X[采样控制切换]
    W -->|KEY4| Y[设置5s周期]
    W -->|KEY5| Z[设置15s周期]
    W -->|KEY6| AA[设置10s周期]
    W -->|无按键| G
    X --> BB[更新采样状态]
    Y --> CC[保存周期参数]
    Z --> CC
    AA --> CC
    BB --> G
    CC --> G
    
    L --> DD[检查串口接收缓冲区]
    DD --> EE{有数据?}
    EE -->|是| FF[读取命令]
    EE -->|否| G
    FF --> GG[命令解析]
    GG --> HH{命令类型}
    HH -->|START| II[启动采样]
    HH -->|STOP| JJ[停止采样]
    HH -->|SET_TIME| KK[设置时间]
    HH -->|其他| LL[其他命令处理]
    II --> G
    JJ --> G
    KK --> G
    LL --> G
    
    M --> MM[检查pre_KeyNum标志]
    MM --> NN{标志位=1?}
    NN -->|是| OO[执行采样状态切换]
    NN -->|否| G
    OO --> PP{当前采样状态}
    PP -->|停止| QQ[启动采样定时器]
    PP -->|运行| RR[停止采样定时器]
    QQ --> SS[更新状态显示]
    RR --> SS
    SS --> G
    
    N --> TT[读取RTC时间]
    TT --> UU[格式化时间字符串]
    UU --> VV[更新OLED显示]
    VV --> G
    
    G --> C
    
    style A fill:#e3f2fd
    style C fill:#fff9c4
    style E fill:#fff9c4
    style I fill:#f3e5f5
    style W fill:#fff9c4
    style HH fill:#fff9c4
```

## 任务调度详解

### 调度器核心逻辑
1. **时间检查**：每个任务检查是否到达执行时间
2. **任务执行**：满足条件的任务立即执行
3. **时间更新**：执行后更新任务的上次运行时间
4. **循环调度**：遍历所有任务后重新开始

### 任务列表和周期
```c
static task_t scheduler_task[] = {
    {adc_flip_task,     5,  0},   // ADC控制任务，5ms周期
    {btn_task,          5,  0},   // 按键处理任务，5ms周期
    {uart_task,         5,  0},   // 串口处理任务，5ms周期
    {rtc_task,         50,  0},   // RTC显示任务，50ms周期
    {adc_process_task,  1,  0},   // ADC处理任务，1ms周期
};
```

### 各任务功能说明

#### ADC数据处理任务（1ms周期）
- **最高优先级**：1ms周期保证数据处理的实时性
- **标志位检查**：检查timer6_execute_flag是否置位
- **数据处理**：ADC值读取、电压转换、超限检测
- **数据存储**：根据模式存储到不同文件夹

#### 按键处理任务（5ms周期）
- **按键扫描**：检测4个按键的状态
- **防抖处理**：软件防抖算法
- **功能切换**：采样控制和周期设置
- **参数保存**：将设置保存到Flash

#### 串口处理任务（5ms周期）
- **数据接收**：从环形缓冲区读取数据
- **命令解析**：解析串口命令
- **功能执行**：执行相应的控制功能
- **状态反馈**：返回执行结果

#### ADC控制任务（5ms周期）
- **状态切换**：检查采样启动/停止标志
- **定时器控制**：启动或停止采样定时器
- **状态显示**：更新LED和OLED显示

#### RTC显示任务（50ms周期）
- **时间读取**：从RTC读取当前时间
- **格式转换**：时间格式化处理
- **显示更新**：更新OLED时间显示

## 调度特点

1. **协作式调度**：任务主动让出CPU，无抢占
2. **时间驱动**：基于系统时间的周期性调度
3. **优先级隐含**：通过执行顺序体现优先级
4. **资源共享**：任务间通过全局变量通信
5. **实时响应**：1ms级别的高频任务保证实时性
