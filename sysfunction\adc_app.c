#include "bsp_gd32f470vet6.h"
#include "uart_ringbuffer.h"

uint32_t adc_val;  // ADC采样值
float Vol_Value;  // ADC采样值转换成电压值
extern uint16_t adc_value[1];
uint8_t adc_running;
// 全局变量标志位，当定时器6执行后置1
volatile uint8_t timer6_execute_flag = 0;

// 优化：预计算ADC转换系数，避免重复除法运算
static const float ADC_CONVERSION_FACTOR = 3.3f / 4095.0f;

/**
 * @brief  ADC采样控制任务函数
 * @param  none
 * @retval none
 * @note   该函数负责控制ADC采样的启动和停止，通过按键触发状态切换
 *         - 当adc_running=1时，按键触发启动采样
 *         - 当adc_running=0时，按键触发停止采样
 *         - 采样周期根据KeyNum值计算：(KeyNum-1)*5秒
 */
void adc_flip_task(void)
{
	// 检查是否有按键触发事件
	if (pre_KeyNum == 1)
	{
		// 计算采样周期：KeyNum=2时为5s，KeyNum=3时为10s，KeyNum=4时为15s
		uint8_t cycle = (KeyNum - 1)*5;

		// 检查当前ADC运行状态
		if (adc_running == 1)
		{
			// ========== ADC采样启动流程 ==========
			adc_running = 0;  // 设置为运行状态（0表示运行，1表示停止）

			// 启动定时器
			timer_enable(TIMER4);  // 启动定时器4（LED闪烁指示）
			timer_enable(TIMER6);  // 启动定时器6（ADC采样定时器）

			// 清除OLED显示
			OLED_Clear();

			// 串口输出启动信息
			my_printf(DEBUG_USART, "\r\nPeriodic Sampling\r\n\r\n");
			my_printf(DEBUG_USART, "sample cycle: %d\r\n", cycle);

			// 设置OLED显示标志
			oled_flag = 1;  // 启用OLED显示ADC数据
		}
		else
		{
			// ========== ADC采样停止流程 ==========
			adc_running = 1;  // 设置为停止状态

			// 停止定时器
			timer_disable(TIMER4);  // 停止定时器4
			timer_disable(TIMER6);  // 停止定时器6

			// 串口输出停止信息
			my_printf(DEBUG_USART, "ADC output stopped\r\n");

			// 清除OLED显示
			OLED_Clear();
			oled_flag = 0;  // 禁用OLED显示

			// 关闭采样指示LED
			LED1_OFF;  // 关闭采样 LED 指示灯，如需要应写成 LED1_ON();
		}

		// 重置按键状态，防止重复触发
		pre_KeyNum = 0;  // 清除按键触发标志，等待下次按键事件
	}



}

/**
 * @brief  ADC数据处理任务函数
 * @param  none
 * @retval none
 * @note   该函数检查timer6_execute_flag标志位，当定时器6触发采样后执行ADC数据处理
 *         主要功能：
 *         1. 读取ADC原始值并转换为电压值
 *         2. 在OLED上显示电压值
 *         3. 根据加密模式选择不同的数据输出和存储方式
 *         4. 进行电压超限检测和报警处理
 *         5. 将数据存储到相应的文件夹中
 */
void adc_process_task(void)
{
    // 检查定时器6执行标志位是否置1
    if (timer6_execute_flag == 1)
    {
        // 清除标志位，防止重复处理
        timer6_execute_flag = 0;

        // ========== ADC数据读取和转换 ==========
        adc_val = adc_value[0];  // 读取ADC原始采样值
        // 优化：使用预计算的转换系数，提高计算效率
        Vol_Value = input_radio * adc_val * ADC_CONVERSION_FACTOR;

        // 在OLED显示屏上显示电压值
        oled_printf(0, 1, "Voltage: %.2fV",Vol_Value );

		// ========== 根据加密模式处理数据输出 ==========
		if (encrypt_mode_enabled) {
			// 加密模式：数据以加密格式输出和存储
			uint32_t timestamp = rtc_to_unix_timestamp_hex();  // 获取时间戳
			store_hide_data(timestamp, Vol_Value);  // 存储到hideData文件夹

			// 加密模式下的串口输出：只输出十六进制加密数据
			uint32_t voltage_hex = voltage_to_hex(Vol_Value);  // 电压值转换为HEX格式

			// 检查是否超限，超限时在数据末尾添加*标记
			if(Vol_Value > input_threshold) {
				my_printf(DEBUG_USART, "%08lX%08lX*\r\n", timestamp, voltage_hex);
			} else {
				my_printf(DEBUG_USART, "%08lX%08lX\r\n", timestamp, voltage_hex);
			}
		}

		// ========== 电压超限检测和处理 ==========
		if(Vol_Value > input_threshold)
		{
			// 超限处理流程
			LED2_ON;  // 点亮超限指示LED
			rtc_adcshow_time();  // 显示当前时间
			my_printf(DEBUG_USART, "  ch0 = %lu, Voltage: %.2fV OverLimit(30.00)!\r\n", adc_val, Vol_Value);

			// 超限数据额外存储到overLimit文件夹（无论是否加密模式都要存储）
			store_overlimit_data(Vol_Value);
		}
		else
		{
			// 正常范围内的处理流程
			LED2_OFF;  // 关闭超限指示LED
			rtc_adcshow_time();  // 显示当前时间
			my_printf(DEBUG_USART, "  ch0 = %lu, Voltage: %.2fV\r\n", adc_val, Vol_Value);

			// 正常模式：所有采样数据都存储到sample文件夹
			store_sample_data(Vol_Value);
		}
    }
}

/**
 * @brief  将电压值转换为4字节HEX格式
 * @param  voltage: 输入电压值（例如12.5V）
 * @retval uint32_t: 4字节HEX格式数据
 * @note   高2字节存储整数部分，低2字节存储小数部分
 *         例如12.5V → 整数12 → 0x000C，小数0.5*65536=32768 → 0x8000
 *         最终结果0x000C8000
 */
uint32_t voltage_to_hex(float voltage)
{
    // 分离整数和小数部分
    uint16_t integer_part = (uint16_t)voltage;  // 整数部分
    float fractional_part = voltage - integer_part;  // 小数部分

    // 将小数部分转换为16位整数：0.0-1.0 映射到 0-65535
    uint16_t fractional_hex = (uint16_t)(fractional_part * 65536.0f);

    // 组合32位数据：高16位整数，低16位小数
    uint32_t result = ((uint32_t)integer_part << 16) | fractional_hex;

    return result;
}

void TIMER6_IRQHandler(void)
{
    static uint8_t cycle_counter = 0;  // 周期计数器

    if (timer_interrupt_flag_get(TIMER6, TIMER_INT_FLAG_UP) == SET)     /* 判断定时器更新中断是否发生 */
    {
			timer_interrupt_flag_clear(TIMER6, TIMER_INT_FLAG_UP);          /* 清除定时器更新中断标志 */
        cycle_counter++;  // 增加周期计数

        // 根据KeyNum值决定执行频率
        uint8_t should_execute = 0;

        if (KeyNum == 3) {
            // KeyNum为3时，每两个周期执行一次
            if (cycle_counter >= 2) {
                should_execute = 1;
                cycle_counter = 0;  // 重置计数器
            }
        }
        else if (KeyNum == 4) {
            // KeyNum为4时，每三个周期执行一次
            if (cycle_counter >= 3) {
                should_execute = 1;
                cycle_counter = 0;  // 重置计数器
            }
        }
        else {
            // 其他情况，每个周期都执行
            should_execute = 1;
            cycle_counter = 0;  // 重置计数器
        }

        // 只有在应该执行时才执行中断函数内容
        if (should_execute) {
            // 设置标志位，通知adc_app.c中的函数执行
            timer6_execute_flag = 1;

        } 
    }
}
