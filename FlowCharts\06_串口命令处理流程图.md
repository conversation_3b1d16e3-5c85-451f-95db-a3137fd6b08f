# 串口命令处理流程图

## 图表说明
本流程图展示了uart_task()函数的串口命令处理逻辑，包括命令接收、解析、执行的完整过程。

## Mermaid代码

```mermaid
graph TD
    A[uart_task开始] --> B[检查环形缓冲区<br/>uart_ringbuffer_available]
    B --> C{有数据可读?}
    C -->|否| D[返回，等待下次调用]
    C -->|是| E[从缓冲区读取数据<br/>uart_ringbuffer_read]
    
    E --> F[数据预处理]
    F --> G{接收到完整命令?<br/>检查换行符}
    G -->|否| H[继续接收数据]
    G -->|是| I[命令解析开始]
    
    H --> D
    I --> J{命令类型判断}
    
    J -->|START| K[启动采样命令]
    J -->|STOP| L[停止采样命令]
    J -->|SET_TIME| M[设置时间命令]
    J -->|SET_RADIO| N[设置变比命令]
    J -->|SET_THRESHOLD| O[设置阈值命令]
    J -->|GET_STATUS| P[获取状态命令]
    J -->|ENCRYPT_ON| Q[开启加密命令]
    J -->|ENCRYPT_OFF| R[关闭加密命令]
    J -->|其他| S[未知命令处理]
    
    K --> T[读取采样周期参数<br/>flash_keynum_read_silent]
    T --> U[启动定时器<br/>TIMER4, TIMER6]
    U --> V[清空OLED显示]
    V --> W[设置显示标志<br/>oled_flag = 1]
    W --> X[输出启动确认信息]
    X --> Y[记录启动日志]
    Y --> Z[返回成功]
    
    L --> AA[停止定时器<br/>TIMER4, TIMER6]
    AA --> BB[清空OLED显示]
    BB --> CC[清除显示标志<br/>oled_flag = 0]
    CC --> DD[关闭LED指示]
    DD --> EE[输出停止确认信息]
    EE --> FF[记录停止日志]
    FF --> GG[重置数据存储系统]
    GG --> Z
    
    M --> HH[解析时间参数]
    HH --> II{时间格式正确?}
    II -->|否| JJ[返回格式错误]
    II -->|是| KK[设置RTC时间]
    KK --> LL{设置成功?}
    LL -->|否| MM[返回设置失败]
    LL -->|是| NN[返回设置成功]
    
    N --> OO[解析变比参数]
    OO --> PP{参数范围正确?<br/>0-100}
    PP -->|否| QQ[返回参数错误]
    PP -->|是| RR[保存变比到Flash]
    RR --> SS[更新全局变量]
    SS --> TT[返回设置成功]
    
    O --> UU[解析阈值参数]
    UU --> VV{参数范围正确?}
    VV -->|否| WW[返回参数错误]
    VV -->|是| XX[保存阈值到Flash]
    XX --> YY[更新全局变量]
    YY --> ZZ[返回设置成功]
    
    P --> AAA[读取系统状态]
    AAA --> BBB[格式化状态信息]
    BBB --> CCC[输出状态信息<br/>采样状态、周期、变比等]
    CCC --> Z
    
    Q --> DDD[设置加密标志<br/>encrypt_mode_enabled = 1]
    DDD --> EEE[输出加密开启确认]
    EEE --> Z
    
    R --> FFF[清除加密标志<br/>encrypt_mode_enabled = 0]
    FFF --> GGG[输出加密关闭确认]
    GGG --> Z
    
    S --> HHH[输出未知命令错误]
    HHH --> III[返回错误]
    
    JJ --> III
    MM --> III
    QQ --> III
    WW --> III
    
    Z --> D
    NN --> D
    TT --> D
    ZZ --> D
    III --> D
    
    style A fill:#e3f2fd
    style C fill:#fff9c4
    style G fill:#fff9c4
    style J fill:#f3e5f5
    style II fill:#fff9c4
    style PP fill:#fff9c4
    style VV fill:#fff9c4
    style LL fill:#fff9c4
    style Z fill:#c8e6c9
    style III fill:#ffebee
```

## 串口命令系统

### 支持的命令列表

#### 1. 采样控制命令
```
START           # 启动采样
STOP            # 停止采样
```

#### 2. 参数设置命令
```
SET_TIME:2025-01-18 14:30:25    # 设置系统时间
SET_RADIO:50                    # 设置变比（0-100）
SET_THRESHOLD:2.5               # 设置阈值电压
```

#### 3. 状态查询命令
```
GET_STATUS      # 获取系统状态
```

#### 4. 加密控制命令
```
ENCRYPT_ON      # 开启加密模式
ENCRYPT_OFF     # 关闭加密模式
```

### 命令处理详解

#### START命令处理流程
```c
if (strcmp((char *)temp_buffer, "START") == 0) {
    // 1. 读取采样周期参数
    KeyNum = flash_keynum_read_silent();
    uint8_t cycle = (KeyNum - 1) * 5;
    
    // 2. 启动定时器
    timer_enable(TIMER4);
    timer_enable(TIMER6);
    
    // 3. 更新显示状态
    OLED_Clear();
    oled_flag = 1;
    
    // 4. 输出确认信息
    my_printf(DEBUG_USART, "Periodic Sampling\r\n");
    my_printf(DEBUG_USART, "sample cycle: %d\r\n", cycle);
    
    // 5. 记录日志
    store_log_entry("sample start - cycle 5s (command)");
}
```

#### SET_TIME命令处理流程
```c
if (strncmp((char *)temp_buffer, "SET_TIME:", 9) == 0) {
    // 1. 提取时间参数
    char *time_str = (char *)temp_buffer + 9;
    
    // 2. 解析时间格式
    rtc_parameter_struct rtc_time;
    if (parse_time_string(time_str, &rtc_time) == SUCCESS) {
        // 3. 设置RTC时间
        if (rtc_set_time(&rtc_time) == SUCCESS) {
            my_printf(DEBUG_USART, "Time set successfully\r\n");
        } else {
            my_printf(DEBUG_USART, "Time set failed\r\n");
        }
    } else {
        my_printf(DEBUG_USART, "Invalid time format\r\n");
    }
}
```

### 环形缓冲区机制

#### 数据接收流程
1. **DMA接收**：串口DMA自动接收数据到缓冲区
2. **环形存储**：使用环形缓冲区避免数据丢失
3. **数据读取**：uart_task定期检查并读取数据
4. **命令组装**：按换行符分割完整命令

#### 缓冲区管理
```c
// 环形缓冲区状态检查
uint16_t available = uart_ringbuffer_available();
if (available > 0) {
    // 读取数据
    uint16_t read_len = uart_ringbuffer_read(temp_buffer, available);
    
    // 查找命令结束符
    for (uint16_t i = 0; i < read_len; i++) {
        if (temp_buffer[i] == '\n' || temp_buffer[i] == '\r') {
            // 找到完整命令，开始解析
            parse_command(command_buffer);
            break;
        }
    }
}
```

### 错误处理机制

#### 参数验证
- **格式检查**：命令格式和参数格式验证
- **范围检查**：参数值范围验证
- **类型检查**：数据类型正确性验证

#### 错误响应
```c
// 错误响应示例
if (parameter_out_of_range) {
    my_printf(DEBUG_USART, "Error: Parameter out of range (0-100)\r\n");
    return COMMAND_ERROR;
}

if (invalid_format) {
    my_printf(DEBUG_USART, "Error: Invalid command format\r\n");
    return COMMAND_ERROR;
}
```

## 通信特性

### 实时响应
- **5ms周期检查**：保证命令及时处理
- **非阻塞处理**：不影响其他任务执行
- **即时反馈**：命令执行结果立即返回

### 可靠性保证
- **环形缓冲区**：防止数据丢失
- **错误检测**：完善的错误检测机制
- **状态同步**：命令执行状态实时同步

### 扩展性设计
- **模块化解析**：易于添加新命令
- **统一接口**：标准的命令处理接口
- **配置灵活**：支持参数化配置
