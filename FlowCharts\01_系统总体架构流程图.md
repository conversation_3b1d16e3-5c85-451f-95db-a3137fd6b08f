# DSB系统总体架构流程图

## 图表说明
本流程图展示了DSB数据采集与存储系统的整体运行逻辑，包括系统启动、任务调度、各功能模块的执行流程和相互关系。

## Mermaid代码

```mermaid
graph TD
    A[系统启动] --> B[硬件初始化]
    B --> C[外设配置]
    C --> D[文件系统初始化]
    D --> E[参数配置加载]
    E --> F[任务调度器初始化]
    F --> G[系统自检]
    G --> H{自检结果}
    H -->|成功| I[进入主循环]
    H -->|失败| J[错误处理]
    J --> K[记录错误日志]
    K --> I
    
    I --> L[任务调度器运行]
    L --> M[ADC处理任务<br/>1ms周期]
    L --> N[按键处理任务<br/>5ms周期]
    L --> O[串口处理任务<br/>5ms周期]
    L --> P[ADC控制任务<br/>5ms周期]
    L --> Q[RTC显示任务<br/>50ms周期]
    
    M --> R[检查采样标志]
    R -->|有数据| S[数据处理]
    R -->|无数据| L
    S --> T[电压转换]
    T --> U[超限检测]
    U --> V[数据存储]
    V --> W[状态显示]
    W --> L
    
    N --> X[按键扫描]
    X --> Y{按键按下}
    Y -->|是| Z[按键处理]
    Y -->|否| L
    Z --> AA[参数设置]
    AA --> L
    
    O --> BB[串口数据接收]
    BB --> CC{有命令}
    CC -->|是| DD[命令解析]
    CC -->|否| L
    DD --> EE[执行命令]
    EE --> L
    
    P --> FF[采样控制]
    FF --> GG{采样状态切换}
    GG -->|启动| HH[启动定时器]
    GG -->|停止| II[停止定时器]
    HH --> L
    II --> L
    
    Q --> JJ[RTC时间读取]
    JJ --> KK[OLED显示更新]
    KK --> L
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style L fill:#fff3e0
    style S fill:#fce4ec
```

## 关键节点说明

### 系统启动阶段
- **系统启动**：main函数开始执行
- **硬件初始化**：LED、按键、OLED、Flash、串口、ADC初始化
- **外设配置**：DMA、定时器等外设配置
- **文件系统初始化**：SD卡FAT文件系统初始化

### 任务调度阶段
- **任务调度器运行**：scheduler_run()函数循环执行
- **5个并发任务**：不同周期的任务并行执行
- **事件驱动**：基于标志位和时间触发的任务执行

### 数据处理流程
- **ADC数据处理**：1ms周期检查采样标志
- **电压转换**：原始ADC值转换为实际电压
- **超限检测**：与阈值比较判断是否超限
- **数据存储**：分类存储到不同文件夹

## 设计特点

1. **模块化设计**：各功能模块独立，便于维护和扩展
2. **事件驱动**：基于标志位的异步处理机制
3. **多任务并发**：不同周期的任务协调执行
4. **错误处理**：完善的异常检测和恢复机制
5. **实时响应**：1ms级别的高频任务保证实时性
